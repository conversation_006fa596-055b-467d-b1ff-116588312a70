from PyQt5.QtWidgets import (
    QW<PERSON>t, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, 
    QPushButton, QLineEdit, QTextEdit, QSpinBox, QProgressBar,
    QTableWidget, QTableWidgetItem, QFileDialog, QMessageBox,
    QComboBox, QCheckBox, QSplitter, QTabWidget
)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QFont
import os
import sys
import json
import time
import threading
from typing import List, Dict, Any

# 添加core模块到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))

try:
    from email_sender import EmailSender, EmailValidator
except ImportError:
    # 如果无法导入，使用模拟类
    class EmailSender:
        def __init__(self, *args, **kwargs):
            self.sent_count = 0
            self.failed_count = 0
            self.stop_flag = False
        
        def send_batch_emails(self, *args, **kwargs):
            return []
        
        def stop_sending(self):
            self.stop_flag = True
        
        def get_stats(self):
            return {}
    
    class EmailValidator:
        @staticmethod
        def validate_email_list(emails):
            return emails, []

class SendingThread(QThread):
    """邮件发送线程"""
    
    progress_updated = pyqtSignal(int, int, dict)  # 当前进度, 总数, 结果
    status_updated = pyqtSignal(str)  # 状态信息
    finished = pyqtSignal(list)  # 发送完成
    
    def __init__(self, accounts, recipients, subject, content, settings):
        super().__init__()
        self.accounts = accounts
        self.recipients = recipients
        self.subject = subject
        self.content = content
        self.settings = settings
        self.sender = None
        self.results = []
    
    def run(self):
        """运行发送任务"""
        try:
            # 创建邮件发送器
            self.sender = EmailSender(
                max_threads=self.settings.get('threads', 20),
                connection_pool_size=self.settings.get('pool_size', 10)
            )
            
            # 加载代理
            if self.settings.get('proxy_file'):
                self.sender.load_proxies(self.settings['proxy_file'])
            
            self.status_updated.emit("正在发送邮件...")
            
            # 发送邮件
            self.results = self.sender.send_batch_emails(
                accounts=self.accounts,
                recipients=self.recipients,
                subject=self.subject,
                content=self.content,
                is_html=self.settings.get('is_html', False),
                delay_range=(
                    self.settings.get('min_delay', 1),
                    self.settings.get('max_delay', 5)
                ),
                max_retries=self.settings.get('retries', 3),
                progress_callback=self.on_progress
            )
            
            self.finished.emit(self.results)
            
        except Exception as e:
            self.status_updated.emit(f"发送失败: {str(e)}")
    
    def on_progress(self, current, total, result):
        """进度回调"""
        self.progress_updated.emit(current, total, result)
    
    def stop(self):
        """停止发送"""
        if self.sender:
            self.sender.stop_sending()

class PluginTab(QWidget):
    """群发邮件插件主界面"""
    
    tab_name = "群发邮件"
    
    def __init__(self):
        super().__init__()
        self.accounts = []
        self.recipients = []
        self.sending_thread = None
        self.results = []
        self.is_sending = False
        
        self.init_ui()
        self.load_settings()
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧控制面板
        left_panel = self.create_control_panel()
        splitter.addWidget(left_panel)
        
        # 右侧监控面板
        right_panel = self.create_monitor_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割比例
        splitter.setSizes([400, 600])
        
        layout.addWidget(splitter)
        self.setLayout(layout)
    
    def create_control_panel(self):
        """创建控制面板"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 账号池管理
        accounts_group = QGroupBox("发件账号池")
        accounts_layout = QVBoxLayout()
        
        # 账号加载
        load_layout = QHBoxLayout()
        self.accounts_file_input = QLineEdit()
        self.accounts_file_input.setPlaceholderText("选择账号池文件...")
        load_layout.addWidget(self.accounts_file_input)
        
        self.load_accounts_btn = QPushButton("浏览")
        self.load_accounts_btn.clicked.connect(self.browse_accounts_file)
        load_layout.addWidget(self.load_accounts_btn)
        
        self.parse_accounts_btn = QPushButton("解析账号")
        self.parse_accounts_btn.clicked.connect(self.parse_accounts)
        load_layout.addWidget(self.parse_accounts_btn)
        
        accounts_layout.addLayout(load_layout)
        
        # 账号状态
        self.accounts_status = QLabel("未加载账号")
        accounts_layout.addWidget(self.accounts_status)
        
        accounts_group.setLayout(accounts_layout)
        layout.addWidget(accounts_group)
        
        # 收件人管理
        recipients_group = QGroupBox("收件人列表")
        recipients_layout = QVBoxLayout()
        
        # 收件人加载
        recip_load_layout = QHBoxLayout()
        self.recipients_file_input = QLineEdit()
        self.recipients_file_input.setPlaceholderText("选择收件人文件...")
        recip_load_layout.addWidget(self.recipients_file_input)
        
        self.load_recipients_btn = QPushButton("浏览")
        self.load_recipients_btn.clicked.connect(self.browse_recipients_file)
        recip_load_layout.addWidget(self.load_recipients_btn)
        
        self.parse_recipients_btn = QPushButton("解析收件人")
        self.parse_recipients_btn.clicked.connect(self.parse_recipients)
        recip_load_layout.addWidget(self.parse_recipients_btn)
        
        recipients_layout.addLayout(recip_load_layout)
        
        # 收件人状态
        self.recipients_status = QLabel("未加载收件人")
        recipients_layout.addWidget(self.recipients_status)
        
        recipients_group.setLayout(recipients_layout)
        layout.addWidget(recipients_group)
        
        # 邮件内容
        content_group = QGroupBox("邮件内容")
        content_layout = QVBoxLayout()
        
        # 主题
        subject_layout = QHBoxLayout()
        subject_layout.addWidget(QLabel("主题:"))
        self.subject_input = QLineEdit()
        self.subject_input.setPlaceholderText("邮件主题...")
        subject_layout.addWidget(self.subject_input)
        content_layout.addLayout(subject_layout)
        
        # 内容
        content_layout.addWidget(QLabel("正文:"))
        self.content_input = QTextEdit()
        self.content_input.setPlaceholderText("邮件正文内容...")
        self.content_input.setMaximumHeight(120)
        content_layout.addWidget(self.content_input)
        
        # HTML选项
        self.html_checkbox = QCheckBox("HTML格式")
        content_layout.addWidget(self.html_checkbox)
        
        content_group.setLayout(content_layout)
        layout.addWidget(content_group)
        
        # 发送参数
        params_group = QGroupBox("发送参数")
        params_layout = QVBoxLayout()
        
        # 线程数
        threads_layout = QHBoxLayout()
        threads_layout.addWidget(QLabel("线程数:"))
        self.threads_spin = QSpinBox()
        self.threads_spin.setRange(1, 100)
        self.threads_spin.setValue(20)
        threads_layout.addWidget(self.threads_spin)
        params_layout.addLayout(threads_layout)
        
        # 延迟设置
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("延迟(秒):"))
        self.min_delay_spin = QSpinBox()
        self.min_delay_spin.setRange(0, 60)
        self.min_delay_spin.setValue(1)
        delay_layout.addWidget(self.min_delay_spin)
        delay_layout.addWidget(QLabel("-"))
        self.max_delay_spin = QSpinBox()
        self.max_delay_spin.setRange(0, 60)
        self.max_delay_spin.setValue(5)
        delay_layout.addWidget(self.max_delay_spin)
        params_layout.addLayout(delay_layout)
        
        # 重试次数
        retry_layout = QHBoxLayout()
        retry_layout.addWidget(QLabel("重试次数:"))
        self.retry_spin = QSpinBox()
        self.retry_spin.setRange(1, 10)
        self.retry_spin.setValue(3)
        retry_layout.addWidget(self.retry_spin)
        params_layout.addLayout(retry_layout)
        
        params_group.setLayout(params_layout)
        layout.addWidget(params_group)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始群发")
        self.start_btn.clicked.connect(self.start_sending)
        control_layout.addWidget(self.start_btn)
        
        self.pause_btn = QPushButton("暂停")
        self.pause_btn.clicked.connect(self.pause_sending)
        self.pause_btn.setEnabled(False)
        control_layout.addWidget(self.pause_btn)
        
        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop_sending)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        layout.addLayout(control_layout)
        
        widget.setLayout(layout)
        return widget
    
    def create_monitor_panel(self):
        """创建监控面板"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 状态信息
        status_group = QGroupBox("发送状态")
        status_layout = QVBoxLayout()
        
        # 进度条
        self.progress_bar = QProgressBar()
        status_layout.addWidget(self.progress_bar)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        self.sent_label = QLabel("已发送: 0")
        self.failed_label = QLabel("失败: 0")
        self.speed_label = QLabel("速度: 0/分钟")
        stats_layout.addWidget(self.sent_label)
        stats_layout.addWidget(self.failed_label)
        stats_layout.addWidget(self.speed_label)
        status_layout.addLayout(stats_layout)
        
        # 状态文本
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # 实时日志
        log_group = QGroupBox("实时日志")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        # 日志控制
        log_control = QHBoxLayout()
        self.clear_log_btn = QPushButton("清空日志")
        self.clear_log_btn.clicked.connect(self.clear_log)
        log_control.addWidget(self.clear_log_btn)
        
        self.export_log_btn = QPushButton("导出日志")
        self.export_log_btn.clicked.connect(self.export_log)
        log_control.addWidget(self.export_log_btn)
        
        log_layout.addLayout(log_control)
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)
        
        # 结果表格
        results_group = QGroupBox("发送结果")
        results_layout = QVBoxLayout()
        
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(5)
        self.results_table.setHorizontalHeaderLabels([
            "收件人", "发件人", "状态", "错误信息", "发送时间"
        ])
        self.results_table.setAlternatingRowColors(True)
        results_layout.addWidget(self.results_table)
        
        # 结果控制
        results_control = QHBoxLayout()
        self.export_results_btn = QPushButton("导出结果")
        self.export_results_btn.clicked.connect(self.export_results)
        results_control.addWidget(self.export_results_btn)
        
        self.export_failed_btn = QPushButton("导出失败")
        self.export_failed_btn.clicked.connect(self.export_failed)
        results_control.addWidget(self.export_failed_btn)
        
        results_layout.addLayout(results_control)
        results_group.setLayout(results_layout)
        layout.addWidget(results_group)
        
        widget.setLayout(layout)
        return widget
    
    def browse_accounts_file(self):
        """选择账号文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择账号池文件", "", 
            "文本文件 (*.txt);;Excel文件 (*.xlsx *.xls);;所有文件 (*.*)"
        )
        if file_path:
            self.accounts_file_input.setText(file_path)
    
    def browse_recipients_file(self):
        """选择收件人文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择收件人文件", "", 
            "文本文件 (*.txt);;Excel文件 (*.xlsx *.xls);;所有文件 (*.*)"
        )
        if file_path:
            self.recipients_file_input.setText(file_path)
    
    def parse_accounts(self):
        """解析账号文件"""
        file_path = self.accounts_file_input.text().strip()
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "文件错误", "请选择有效的账号文件")
            return
        
        try:
            self.accounts = []
            
            if file_path.endswith('.txt'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and '----' in line:
                            parts = line.split('----')
                            if len(parts) >= 2:
                                email, password = parts[0], parts[1]
                                name = parts[2] if len(parts) > 2 else ""
                                self.accounts.append((email, password, name))
            
            elif file_path.endswith(('.xlsx', '.xls')):
                import openpyxl
                wb = openpyxl.load_workbook(file_path)
                ws = wb.active
                for row in ws.iter_rows(min_row=2, values_only=True):
                    if row[0] and row[1]:
                        email, password = str(row[0]), str(row[1])
                        name = str(row[2]) if len(row) > 2 and row[2] else ""
                        self.accounts.append((email, password, name))
            
            self.accounts_status.setText(f"已加载 {len(self.accounts)} 个账号")
            self.log(f"成功加载 {len(self.accounts)} 个发件账号")
            
        except Exception as e:
            QMessageBox.critical(self, "解析错误", f"账号文件解析失败:\n{str(e)}")
            self.log(f"账号解析失败: {str(e)}")
    
    def parse_recipients(self):
        """解析收件人文件"""
        file_path = self.recipients_file_input.text().strip()
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "文件错误", "请选择有效的收件人文件")
            return
        
        try:
            self.recipients = []
            
            if file_path.endswith('.txt'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        email = line.strip()
                        if email and '@' in email:
                            self.recipients.append(email)
            
            elif file_path.endswith(('.xlsx', '.xls')):
                import openpyxl
                wb = openpyxl.load_workbook(file_path)
                ws = wb.active
                for row in ws.iter_rows(values_only=True):
                    if row[0] and '@' in str(row[0]):
                        self.recipients.append(str(row[0]).strip())
            
            # 验证邮箱
            valid_emails, invalid_emails = EmailValidator.validate_email_list(self.recipients)
            self.recipients = valid_emails
            
            self.recipients_status.setText(f"已加载 {len(self.recipients)} 个收件人")
            self.log(f"成功加载 {len(self.recipients)} 个收件人，无效邮箱 {len(invalid_emails)} 个")
            
        except Exception as e:
            QMessageBox.critical(self, "解析错误", f"收件人文件解析失败:\n{str(e)}")
            self.log(f"收件人解析失败: {str(e)}")
    
    def start_sending(self):
        """开始发送"""
        # 验证输入
        if not self.accounts:
            QMessageBox.warning(self, "缺少账号", "请先加载发件账号")
            return
        
        if not self.recipients:
            QMessageBox.warning(self, "缺少收件人", "请先加载收件人列表")
            return
        
        subject = self.subject_input.text().strip()
        content = self.content_input.toPlainText().strip()
        
        if not subject or not content:
            QMessageBox.warning(self, "内容不完整", "请填写邮件主题和正文")
            return
        
        # 准备发送参数
        settings = {
            'threads': self.threads_spin.value(),
            'min_delay': self.min_delay_spin.value(),
            'max_delay': self.max_delay_spin.value(),
            'retries': self.retry_spin.value(),
            'is_html': self.html_checkbox.isChecked(),
            'pool_size': min(20, len(self.accounts))
        }
        
        # 启动发送线程
        self.sending_thread = SendingThread(
            self.accounts, self.recipients, subject, content, settings
        )
        
        self.sending_thread.progress_updated.connect(self.on_progress_updated)
        self.sending_thread.status_updated.connect(self.on_status_updated)
        self.sending_thread.finished.connect(self.on_sending_finished)
        
        self.sending_thread.start()
        
        # 更新UI状态
        self.is_sending = True
        self.start_btn.setEnabled(False)
        self.pause_btn.setEnabled(True)
        self.stop_btn.setEnabled(True)
        
        self.results = []
        self.results_table.setRowCount(0)
        self.progress_bar.setValue(0)
        self.progress_bar.setMaximum(len(self.recipients))
        
        self.log(f"开始群发邮件: {len(self.recipients)} 个收件人, {len(self.accounts)} 个账号")
    
    def pause_sending(self):
        """暂停发送"""
        # TODO: 实现暂停功能
        self.log("暂停功能待实现")
    
    def stop_sending(self):
        """停止发送"""
        if self.sending_thread:
            self.sending_thread.stop()
            self.log("正在停止发送...")
    
    def on_progress_updated(self, current, total, result):
        """进度更新"""
        self.progress_bar.setValue(current)
        self.results.append(result)
        
        # 更新结果表格
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)
        
        self.results_table.setItem(row, 0, QTableWidgetItem(result['recipient']))
        self.results_table.setItem(row, 1, QTableWidgetItem(result['sender']))
        self.results_table.setItem(row, 2, QTableWidgetItem("成功" if result['success'] else "失败"))
        self.results_table.setItem(row, 3, QTableWidgetItem(result.get('error', '')))
        self.results_table.setItem(row, 4, QTableWidgetItem(
            time.strftime("%H:%M:%S", time.localtime(result.get('send_time', time.time())))
        ))
        
        # 自动滚动到最新
        self.results_table.scrollToBottom()
        
        # 日志
        status = "成功" if result['success'] else f"失败({result.get('error', '未知错误')})"
        self.log(f"[{current}/{total}] {result['sender']} -> {result['recipient']}: {status}")
    
    def on_status_updated(self, status):
        """状态更新"""
        self.status_label.setText(status)
        self.log(status)
    
    def on_sending_finished(self, results):
        """发送完成"""
        self.is_sending = False
        self.start_btn.setEnabled(True)
        self.pause_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        failed_count = len(results) - success_count
        
        self.log(f"群发完成! 成功: {success_count}, 失败: {failed_count}")
        
        QMessageBox.information(
            self, "发送完成", 
            f"邮件群发已完成!\n\n成功发送: {success_count} 封\n发送失败: {failed_count} 封"
        )
    
    def update_status(self):
        """更新状态信息"""
        if not self.is_sending:
            return
        
        if self.sending_thread and self.sending_thread.sender:
            stats = self.sending_thread.sender.get_stats()
            self.sent_label.setText(f"已发送: {stats.get('current_sent', 0)}")
            self.failed_label.setText(f"失败: {stats.get('current_failed', 0)}")
            self.speed_label.setText(f"速度: {stats.get('emails_per_minute', 0):.1f}/分钟")
    
    def log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
    
    def export_results(self):
        """导出所有结果"""
        if not self.results:
            QMessageBox.warning(self, "无数据", "没有可导出的结果")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出发送结果", f"send_results_{int(time.time())}.txt", 
            "文本文件 (*.txt);;Excel文件 (*.xlsx)"
        )
        if file_path:
            try:
                if file_path.endswith('.txt'):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write("收件人\t发件人\t状态\t错误信息\t发送时间\n")
                        for result in self.results:
                            status = "成功" if result['success'] else "失败"
                            error = result.get('error', '')
                            send_time = time.strftime(
                                "%Y-%m-%d %H:%M:%S", 
                                time.localtime(result.get('send_time', time.time()))
                            )
                            f.write(f"{result['recipient']}\t{result['sender']}\t{status}\t{error}\t{send_time}\n")
                
                elif file_path.endswith('.xlsx'):
                    import openpyxl
                    wb = openpyxl.Workbook()
                    ws = wb.active
                    ws.title = "发送结果"
                    
                    # 写入标题
                    headers = ["收件人", "发件人", "状态", "错误信息", "发送时间"]
                    for col, header in enumerate(headers, 1):
                        ws.cell(row=1, column=col, value=header)
                    
                    # 写入数据
                    for row, result in enumerate(self.results, 2):
                        ws.cell(row, 1, result['recipient'])
                        ws.cell(row, 2, result['sender'])
                        ws.cell(row, 3, "成功" if result['success'] else "失败")
                        ws.cell(row, 4, result.get('error', ''))
                        ws.cell(row, 5, time.strftime(
                            "%Y-%m-%d %H:%M:%S", 
                            time.localtime(result.get('send_time', time.time()))
                        ))
                    
                    wb.save(file_path)
                
                QMessageBox.information(self, "导出成功", f"结果已导出到:\n{file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"结果导出失败:\n{str(e)}")
    
    def export_failed(self):
        """导出失败邮箱"""
        if not self.results:
            QMessageBox.warning(self, "无数据", "没有可导出的结果")
            return
        
        failed_results = [r for r in self.results if not r['success']]
        if not failed_results:
            QMessageBox.information(self, "无失败邮箱", "所有邮件都发送成功了")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出失败邮箱", f"failed_emails_{int(time.time())}.txt", 
            "文本文件 (*.txt)"
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    for result in failed_results:
                        f.write(f"{result['recipient']}\n")
                
                QMessageBox.information(
                    self, "导出成功", 
                    f"已导出 {len(failed_results)} 个失败邮箱到:\n{file_path}"
                )
                
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"失败邮箱导出失败:\n{str(e)}")
    
    def load_settings(self):
        """加载设置"""
        try:
            settings_file = os.path.join("config", "mailer_settings.json")
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                # 恢复设置
                self.threads_spin.setValue(settings.get('threads', 20))
                self.min_delay_spin.setValue(settings.get('min_delay', 1))
                self.max_delay_spin.setValue(settings.get('max_delay', 5))
                self.retry_spin.setValue(settings.get('retries', 3))
                self.html_checkbox.setChecked(settings.get('is_html', False))
                
        except Exception as e:
            self.log(f"设置加载失败: {str(e)}")
    
    def save_settings(self):
        """保存设置"""
        try:
            settings = {
                'threads': self.threads_spin.value(),
                'min_delay': self.min_delay_spin.value(),
                'max_delay': self.max_delay_spin.value(),
                'retries': self.retry_spin.value(),
                'is_html': self.html_checkbox.isChecked()
            }
            
            os.makedirs("config", exist_ok=True)
            settings_file = os.path.join("config", "mailer_settings.json")
            
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.log(f"设置保存失败: {str(e)}")
    
    def closeEvent(self, event):
        """关闭事件"""
        # 保存设置
        self.save_settings()
        
        # 停止发送线程
        if self.sending_thread and self.sending_thread.isRunning():
            self.sending_thread.stop()
            self.sending_thread.wait(3000)  # 等待3秒
        
        event.accept()_log(self):
        """导出日志"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出日志", f"send_log_{int(time.time())}.txt", 
            "文本文件 (*.txt)"
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                QMessageBox.information(self, "导出成功", f"日志已导出到:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"日志导出失败:\n{str(e)}")
    
    def export