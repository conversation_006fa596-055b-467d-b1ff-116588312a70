from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QFileDialog, QMessageBox, QGroupBox
import shutil
import os
import datetime

ARCHIVE_DIR = "data/archive"
os.makedirs(ARCHIVE_DIR, exist_ok=True)

class PluginTab(QWidget):
    tab_name = "归档&备份"

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 异常账号一键归档
        box1 = QGroupBox("异常账号管理")
        l1 = QHBoxLayout()
        self.archive_btn = QPushButton("一键归档异常账号")
        self.archive_btn.clicked.connect(self.archive_accounts)
        l1.addWidget(self.archive_btn)
        self.export_btn = QPushButton("导出异常账号池")
        self.export_btn.clicked.connect(self.export_archive)
        l1.addWidget(self.export_btn)
        box1.setLayout(l1)
        layout.addWidget(box1)

        # 账号池/配置一键备份还原
        box2 = QGroupBox("批量备份/还原")
        l2 = QHBoxLayout()
        self.backup_btn = QPushButton("一键备份所有数据")
        self.backup_btn.clicked.connect(self.backup_all)
        l2.addWidget(self.backup_btn)
        self.restore_btn = QPushButton("一键还原全部数据")
        self.restore_btn.clicked.connect(self.restore_all)
        l2.addWidget(self.restore_btn)
        box2.setLayout(l2)
        layout.addWidget(box2)

        # 日志区
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        layout.addWidget(self.log_box)

        self.setLayout(layout)

    def archive_accounts(self):
        # 模拟，将当前目录所有名为 error_accounts.txt 的账号池，全部搬到归档区
        err_files = []
        for root, _, files in os.walk("."):
            for fname in files:
                if fname == "error_accounts.txt":
                    full_path = os.path.join(root, fname)
                    arch_name = f"error_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                    arch_path = os.path.join(ARCHIVE_DIR, arch_name)
                    shutil.copy(full_path, arch_path)
                    err_files.append(arch_path)
        self.log_box.append(f"已归档异常账号池文件数: {len(err_files)}")

    def export_archive(self):
        files = [f for f in os.listdir(ARCHIVE_DIR) if f.endswith(".txt")]
        if not files:
            QMessageBox.warning(self, "导出失败", "无异常账号可导出！")
            return
        sel_file = files[-1]
        src = os.path.join(ARCHIVE_DIR, sel_file)
        fname, _ = QFileDialog.getSaveFileName(self, "导出异常账号池", sel_file, "Text Files (*.txt)")
        if fname:
            shutil.copy(src, fname)
            QMessageBox.information(self, "导出成功", f"异常账号已导出到:\n{fname}")

    def backup_all(self):
        # 备份 data、config 目录到本地 zip
        backup_name = f"backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
        backup_path = os.path.join(ARCHIVE_DIR, backup_name)
        import zipfile
        with zipfile.ZipFile(backup_path, 'w') as z:
            for folder in ["data", "config"]:
                if not os.path.exists(folder): continue
                for root, _, files in os.walk(folder):
                    for f in files:
                        file_path = os.path.join(root, f)
                        z.write(file_path)
        self.log_box.append(f"已完成全量备份：{backup_path}")

    def restore_all(self):
        # 选择 zip 文件恢复 data、config 目录
        fname, _ = QFileDialog.getOpenFileName(self, "选择备份包", ARCHIVE_DIR, "ZIP Files (*.zip)")
        if not fname:
            return
        import zipfile
        with zipfile.ZipFile(fname, 'r') as z:
            z.extractall(".")
        self.log_box.append("备份已还原，重启软件后生效！")
