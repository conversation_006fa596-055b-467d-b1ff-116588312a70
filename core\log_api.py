import os
import json
import time

LOG_DIR = "logs/"
os.makedirs(LOG_DIR, exist_ok=True)

def log_event(event, detail=None):
    now = time.strftime("%Y-%m-%d %H:%M:%S")
    log = {"time": now, "event": event, "detail": detail}
    with open(os.path.join(LOG_DIR, "global.log"), "a", encoding="utf-8") as f:
        f.write(json.dumps(log, ensure_ascii=False) + "\n")

def get_logs(limit=100):
    path = os.path.join(LOG_DIR, "global.log")
    if not os.path.exists(path):
        return []
    with open(path, "r", encoding="utf-8") as f:
        lines = f.readlines()
    logs = [json.loads(l) for l in lines[-limit:]]
    return logs[::-1]
