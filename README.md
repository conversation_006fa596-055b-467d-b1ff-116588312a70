# EM Sent 邮件群发工具 v2.0

<div align="center">
  <h3>🚀 专业级邮件群发工具 | Professional Email Marketing Tool</h3>
  <p>
    <img src="https://img.shields.io/badge/version-2.0.0-blue.svg" alt="Version">
    <img src="https://img.shields.io/badge/python-3.8+-green.svg" alt="Python">
    <img src="https://img.shields.io/badge/platform-Windows%20%7C%20macOS%20%7C%20Linux-lightgrey.svg" alt="Platform">
    <img src="https://img.shields.io/badge/license-MIT-orange.svg" alt="License">
  </p>
</div>

## 📖 项目简介

EM Sent 是一款功能强大的邮件群发工具，支持多账号轮询、智能内容生成、实时监控等高级功能。适用于邮件营销、客户通知、批量邮件发送等场景。

### ✨ 核心特性

- 🎯 **多账号支持** - Gmail、QQ、163、Outlook等主流邮箱
- 🚀 **高效群发** - 多线程并发，支持大批量发送
- 🔍 **智能检测** - 邮箱验证、MX记录检查、临时邮箱过滤
- 🤖 **AI内容生成** - 智能生成邮件标题和正文内容
- 📊 **实时监控** - 发送进度、成功率统计、错误分析
- 🔄 **自动化流程** - 任务计划、宏录制、批量操作
- 🛡️ **安全可靠** - 代理池支持、连接池管理、失败重试

## 🏗️ 系统架构

```
EM Sent Tool
├── 核心引擎 (Core Engine)
│   ├── 邮件发送引擎 - 多SMTP支持、连接池
│   ├── 配置管理器 - 统一配置管理
│   └── 数据处理API - 安全的数据操作
├── 插件系统 (Plugin System)
│   ├── 群发邮件 - 核心发送功能
│   ├── 账号管理 - 发件账号管理
│   ├── 内容模板 - 邮件模板库
│   ├── AI生成 - 智能内容创作
│   ├── 收件人检测 - 邮箱有效性验证
│   └── 其他20+插件...
└── 用户界面 (User Interface)
    ├── 现代化GUI - PyQt5界面
    ├── 实时监控 - 进度追踪
    └── 数据可视化 - 统计报表
```

## 🚀 快速开始

### 环境要求

- **Python**: 3.8 或以上版本
- **操作系统**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **内存**: 最少 4GB RAM
- **磁盘**: 最少 2GB 可用空间

### .安装步骤

1. **安装依赖**
```bash
# 使用pip安装
pip install -r requirements.txt

# 或使用自动安装脚本
python install.py
```

2. **启动程序**
```bash
python main.py
```

### 首次使用配置

1. **账号配置** - 在"账号管理"中导入发件邮箱
2. **收件人导入** - 上传收件人列表文件
3. **邮件内容** - 编写或使用AI生成邮件内容
4. **参数设置** - 配置发送线程数、延迟等参数

## 📚 功能指南

### 🔥 核心功能

#### 1. 群发邮件
- **多账号轮询** - 自动切换发件账号避免限制
- **智能限流** - 可配置发送间隔和速度控制
- **实时监控** - 查看发送进度、成功率、失败原因
- **结果导出** - 导出发送结果、失败邮箱列表

**使用步骤**:
1. 点击"群发邮件"标签页
2. 加载账号池文件 (格式: `邮箱----密码----发件人名称`)
3. 加载收件人文件 (每行一个邮箱地址)
4. 填写邮件主题和正文
5. 设置发送参数 (线程数、延迟、重试次数)
6. 点击"开始群发"

#### 2. 账号管理
- **批量导入** - 支持TXT、Excel格式账号池
- **账号验证** - 自动检测账号有效性
- **分类管理** - 健康账号、异常账号分类

**账号池格式**:
```
<EMAIL>----password123----测试用户1
<EMAIL>----password456----测试用户2
<EMAIL>----password789----测试用户3
```

#### 3. 收件人检测
- **语法验证** - 检查邮箱格式正确性
- **MX记录检查** - 验证域名邮件服务器
- **临时邮箱过滤** - 过滤一次性邮箱
- **去重处理** - 自动去除重复邮箱

#### 4. AI内容生成
- **智能标题** - 根据关键词生成吸引人的标题
- **正文创作** - 自动生成邮件正文内容
- **批量模板** - 一次生成多个内容变体
- **内容优化** - 避免垃圾邮件关键词

### 🛠️ 高级功能

#### 1. 任务计划
- **定时发送** - 设置特定时间自动执行
- **循环任务** - 支持每日、每周重复执行
- **条件触发** - 基于特定条件启动任务

#### 2. 流程编辑器
- **可视化流程** - 拖拽式流程设计
- **自动化执行** - 一键执行复杂流程
- **流程模板** - 保存和复用常用流程

#### 3. 数据分析
- **发送统计** - 成功率、失败率分析
- **账号活跃度** - 各账号使用情况统计
- **错误分析** - 失败原因分类统计
- **报表导出** - 生成详细统计报表

#### 4. 系统监控
- **SMTP监控** - 实时监控连接状态
- **性能监控** - CPU、内存使用情况
- **自动体检** - 系统健康状况检查

## 🔧 配置说明

### 主配置文件 (`config/app_config.json`)

```json
{
  "app": {
    "title": "EM Sent 邮件群发工具",
    "version": "2.0.0",
    "window_width": 1200,
    "window_height": 720,
    "theme": "default",
    "language": "zh_CN"
  },
  "email": {
    "smtp