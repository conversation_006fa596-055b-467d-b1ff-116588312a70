# 通用沙箱数据接口（供插件安全读写主数据，只需import sandbox_api调用）
import os, shutil, json, time

DATA_DIR = "data/"
BACKUP_DIR = "backup/"
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(BACKUP_DIR, exist_ok=True)

def backup_data(file):
    ts = time.strftime("%Y%m%d_%H%M%S")
    base = os.path.basename(file)
    shutil.copy(file, os.path.join(BACKUP_DIR, f"{base}_{ts}"))

def safe_write(file, lines):
    """只能写白名单路径，写前自动快照"""
    full_path = os.path.join(DATA_DIR, file)
    if os.path.exists(full_path):
        backup_data(full_path)
    with open(full_path, "w", encoding="utf-8") as f:
        f.writelines(lines if isinstance(lines, list) else [lines])

def safe_read(file):
    full_path = os.path.join(DATA_DIR, file)
    if not os.path.exists(full_path):
        return []
    with open(full_path, "r", encoding="utf-8") as f:
        return f.readlines()

def safe_json_write(file, obj):
    full_path = os.path.join(DATA_DIR, file)
    if os.path.exists(full_path):
        backup_data(full_path)
    with open(full_path, "w", encoding="utf-8") as f:
        json.dump(obj, f, ensure_ascii=False, indent=2)

def safe_json_read(file):
    full_path = os.path.join(DATA_DIR, file)
    if not os.path.exists(full_path):
        return {}
    with open(full_path, "r", encoding="utf-8") as f:
        return json.load(f)
