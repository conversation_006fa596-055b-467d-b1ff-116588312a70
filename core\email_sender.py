import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import List, Dict, Optional, Tuple
import time
import logging
from core.log_api import log_event

class EmailSender:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.smtp_servers = {
            'gmail.com': ('smtp.gmail.com', 587),
            'qq.com': ('smtp.qq.com', 587),
            '163.com': ('smtp.163.com', 587),
            'outlook.com': ('smtp.live.com', 587),
            'hotmail.com': ('smtp.live.com', 587),
            'yahoo.com': ('smtp.mail.yahoo.com', 587)
        }
    
    def get_smtp_config(self, email: str) -> Tuple[str, int]:
        domain = email.split('@')[1].lower()
        return self.smtp_servers.get(domain, ('smtp.gmail.com', 587))
    
    def send_single_email(self, sender_email: str, sender_password: str, 
                         recipient: str, subject: str, content: str, 
                         is_html: bool = False) -> Dict[str, any]:
        try:
            smtp_server, smtp_port = self.get_smtp_config(sender_email)
            
            message = MIMEMultipart()
            message["From"] = sender_email
            message["To"] = recipient
            message["Subject"] = subject
            
            content_type = "html" if is_html else "plain"
            message.attach(MIMEText(content, content_type))
            
            context = ssl.create_default_context()
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls(context=context)
                server.login(sender_email, sender_password)
                server.sendmail(sender_email, recipient, message.as_string())
            
            log_event("邮件发送成功", f"{sender_email} -> {recipient}")
            return {"success": True, "message": "发送成功"}
            
        except Exception as e:
            error_msg = f"发送失败: {str(e)}"
            log_event("邮件发送失败", f"{sender_email} -> {recipient}: {error_msg}")
            return {"success": False, "message": error_msg}
    
    def send_batch_emails(self, sender_email: str, sender_password: str,
                         recipients: List[str], subject: str, content: str,
                         is_html: bool = False, batch_size: int = 50,
                         delay: int = 2) -> Dict[str, any]:
        results = {"success": [], "failed": [], "total": len(recipients)}
        
        for i in range(0, len(recipients), batch_size):
            batch = recipients[i:i + batch_size]
            
            for recipient in batch:
                result = self.send_single_email(
                    sender_email, sender_password, recipient, 
                    subject, content, is_html
                )
                
                if result["success"]:
                    results["success"].append(recipient)
                else:
                    results["failed"].append({
                        "email": recipient,
                        "error
                        import smtplib
import ssl
import socket
import socks
import time
import random
import threading
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
from email.header import Header
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue
import imaplib

logger = logging.getLogger(__name__)

class SMTPConfig:
    """SMTP服务器配置"""
    
    SMTP_SERVERS = {
        # Gmail
        'gmail.com': {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'use_tls': True,
            'imap_server': 'imap.gmail.com',
            'imap_port': 993
        },
        # QQ邮箱
        'qq.com': {
            'smtp_server': 'smtp.qq.com', 
            'smtp_port': 587,
            'use_tls': True,
            'imap_server': 'imap.qq.com',
            'imap_port': 993
        },
        # 163邮箱
        '163.com': {
            'smtp_server': 'smtp.163.com',
            'smtp_port': 587,
            'use_tls': True,
            'imap_server': 'imap.163.com',
            'imap_port': 993
        },
        # Outlook/Hotmail
        'outlook.com': {
            'smtp_server': 'smtp-mail.outlook.com',
            'smtp_port': 587,
            'use_tls': True,
            'imap_server': 'outlook.office365.com',
            'imap_port': 993
        },
        'hotmail.com': {
            'smtp_server': 'smtp-mail.outlook.com',
            'smtp_port': 587,
            'use_tls': True,
            'imap_server': 'outlook.office365.com',
            'imap_port': 993
        },
        # Yahoo
        'yahoo.com': {
            'smtp_server': 'smtp.mail.yahoo.com',
            'smtp_port': 587,
            'use_tls': True,
            'imap_server': 'imap.mail.yahoo.com',
            'imap_port': 993
        }
    }
    
    @classmethod
    def get_config(cls, email: str) -> Dict[str, Any]:
        """根据邮箱地址获取SMTP配置"""
        domain = email.split('@')[1].lower()
        return cls.SMTP_SERVERS.get(domain, cls.SMTP_SERVERS['gmail.com'])

class ProxyManager:
    """代理管理器"""
    
    def __init__(self):
        self.proxies = []
        self.current_index = 0
        self.lock = threading.Lock()
    
    def load_proxies(self, proxy_list: List[str]):
        """加载代理列表"""
        self.proxies = []
        for proxy in proxy_list:
            try:
                if '://' in proxy:
                    # 格式: socks5://127.0.0.1:1080
                    proxy_type, address = proxy.split('://')
                    host, port = address.split(':')
                    self.proxies.append({
                        'type': proxy_type,
                        'host': host,
                        'port': int(port)
                    })
                else:
                    # 格式: 127.0.0.1:1080
                    host, port = proxy.split(':')
                    self.proxies.append({
                        'type': 'socks5',
                        'host': host,
                        'port': int(port)
                    })
            except Exception as e:
                logger.warning(f"代理格式错误: {proxy}, {e}")
    
    def get_proxy(self) -> Optional[Dict[str, Any]]:
        """获取下一个代理"""
        if not self.proxies:
            return None
        
        with self.lock:
            proxy = self.proxies[self.current_index]
            self.current_index = (self.current_index + 1) % len(self.proxies)
            return proxy
    
    def setup_proxy(self, proxy: Dict[str, Any]):
        """设置代理"""
        if proxy['type'] == 'socks5':
            socks.set_default_proxy(socks.SOCKS5, proxy['host'], proxy['port'])
            socket.socket = socks.socksocket

class SMTPConnectionPool:
    """SMTP连接池"""
    
    def __init__(self, max_connections: int = 10):
        self.max_connections = max_connections
        self.connections = Queue(maxsize=max_connections)
        self.active_connections = {}
        self.lock = threading.Lock()
    
    def get_connection(self, email: str, password: str, proxy: Optional[Dict] = None) -> Optional[smtplib.SMTP]:
        """获取SMTP连接"""
        connection_key = f"{email}:{hash(password)}"
        
        # 尝试从池中获取连接
        if not self.connections.empty():
            try:
                conn = self.connections.get_nowait()
                if self._test_connection(conn):
                    return conn
                else:
                    conn.quit()
            except:
                pass
        
        # 创建新连接
        return self._create_connection(email, password, proxy)
    
    def return_connection(self, conn: smtplib.SMTP):
        """归还连接到池"""
        if conn and self._test_connection(conn):
            try:
                self.connections.put_nowait(conn)
            except:
                conn.quit()
        elif conn:
            conn.quit()
    
    def _create_connection(self, email: str, password: str, proxy: Optional[Dict] = None) -> Optional[smtplib.SMTP]:
        """创建新的SMTP连接"""
        try:
            config = SMTPConfig.get_config(email)
            
            # 设置代理
            if proxy:
                ProxyManager().setup_proxy(proxy)
            
            # 创建SMTP连接
            server = smtplib.SMTP(config['smtp_server'], config['smtp_port'], timeout=30)
            server.ehlo()
            
            if config['use_tls']:
                context = ssl.create_default_context()
                server.starttls(context=context)
                server.ehlo()
            
            server.login(email, password)
            logger.info(f"SMTP连接创建成功: {email}")
            return server
            
        except Exception as e:
            logger.error(f"SMTP连接失败 {email}: {e}")
            return None
    
    def _test_connection(self, conn: smtplib.SMTP) -> bool:
        """测试连接是否有效"""
        try:
            conn.noop()
            return True
        except:
            return False

class EmailSender:
    """邮件发送器"""
    
    def __init__(self, max_threads: int = 50, connection_pool_size: int = 20):
        self.max_threads = max_threads
        self.connection_pool = SMTPConnectionPool(connection_pool_size)
        self.proxy_manager = ProxyManager()
        self.sent_count = 0
        self.failed_count = 0
        self.lock = threading.Lock()
        self.stop_flag = False
        
        # 统计信息
        self.stats = {
            'total_sent': 0,
            'total_failed': 0,
            'start_time': None,
            'accounts_used': set(),
            'error_stats': {}
        }
    
    def load_proxies(self, proxy_file: str):
        """加载代理列表"""
        try:
            with open(proxy_file, 'r', encoding='utf-8') as f:
                proxies = [line.strip() for line in f if line.strip()]
            self.proxy_manager.load_proxies(proxies)
            logger.info(f"已加载 {len(proxies)} 个代理")
        except Exception as e:
            logger.error(f"加载代理失败: {e}")
    
    def create_message(self, sender_email: str, sender_name: str, recipient: str, 
                      subject: str, content: str, is_html: bool = False,
                      attachments: Optional[List[str]] = None) -> MIMEMultipart:
        """创建邮件消息"""
        msg = MIMEMultipart()
        
        # 设置发件人
        if sender_name:
            msg['From'] = f"{sender_name} <{sender_email}>"
        else:
            msg['From'] = sender_email
        
        msg['To'] = recipient
        msg['Subject'] = Header(subject, 'utf-8')
        
        # 添加邮件正文
        content_type = 'html' if is_html else 'plain'
        msg.attach(MIMEText(content, content_type, 'utf-8'))
        
        # 添加附件
        if attachments:
            for file_path in attachments:
                try:
                    with open(file_path, 'rb') as f:
                        attach = MIMEApplication(f.read())
                        attach.add_header(
                            'Content-Disposition', 
                            'attachment', 
                            filename=os.path.basename(file_path)
                        )
                        msg.attach(attach)
                except Exception as e:
                    logger.warning(f"添加附件失败 {file_path}: {e}")
        
        return msg
    
    def send_single_email(self, sender_email: str, sender_password: str, 
                         sender_name: str, recipient: str, subject: str, 
                         content: str, is_html: bool = False,
                         attachments: Optional[List[str]] = None,
                         max_retries: int = 3) -> Dict[str, Any]:
        """发送单封邮件"""
        result = {
            'success': False,
            'recipient': recipient,
            'sender': sender_email,
            'error': None,
            'attempts': 0,
            'send_time': None
        }
        
        for attempt in range(max_retries):
            result['attempts'] = attempt + 1
            
            try:
                # 获取代理
                proxy = self.proxy_manager.get_proxy()
                
                # 获取SMTP连接
                conn = self.connection_pool.get_connection(sender_email, sender_password, proxy)
                if not conn:
                    result['error'] = "无法建立SMTP连接"
                    continue
                
                # 创建邮件
                msg = self.create_message(
                    sender_email, sender_name, recipient, 
                    subject, content, is_html, attachments
                )
                
                # 发送邮件
                conn.send_message(msg)
                
                # 归还连接
                self.connection_pool.return_connection(conn)
                
                # 成功
                result['success'] = True
                result['send_time'] = time.time()
                
                with self.lock:
                    self.sent_count += 1
                    self.stats['total_sent'] += 1
                    self.stats['accounts_used'].add(sender_email)
                
                logger.info(f"邮件发送成功: {sender_email} -> {recipient}")
                break
                
            except Exception as e:
                error_msg = str(e)
                result['error'] = error_msg
                
                with self.lock:
                    error_type = type(e).__name__
                    self.stats['error_stats'][error_type] = self.stats['error_stats'].get(error_type, 0) + 1
                
                logger.warning(f"发送失败 (尝试 {attempt + 1}/{max_retries}): {sender_email} -> {recipient}, 错误: {error_msg}")
                
                # 等待后重试
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(1, 3))
        
        if not result['success']:
            with self.lock:
                self.failed_count += 1
                self.stats['total_failed'] += 1
        
        return result
    
    def send_batch_emails(self, accounts: List[Tuple[str, str, str]], 
                         recipients: List[str], subject: str, content: str,
                         is_html: bool = False, delay_range: Tuple[int, int] = (1, 5),
                         max_retries: int = 3, attachments: Optional[List[str]] = None,
                         progress_callback: Optional[callable] = None) -> List[Dict[str, Any]]:
        """批量发送邮件"""
        if not accounts or not recipients:
            return []
        
        self.stats['start_time'] = time.time()
        self.sent_count = 0
        self.failed_count = 0
        self.stop_flag = False
        
        results = []
        total_emails = len(recipients)
        
        # 创建任务列表
        tasks = []
        for i, recipient in enumerate(recipients):
            # 轮询账号
            account = accounts[i % len(accounts)]
            sender_email, sender_password, sender_name = account
            
            tasks.append({
                'sender_email': sender_email,
                'sender_password': sender_password,
                'sender_name': sender_name,
                'recipient': recipient,
                'subject': subject,
                'content': content,
                'is_html': is_html,
                'attachments': attachments,
                'max_retries': max_retries,
                'index': i
            })
        
        # 使用线程池发送
        with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
            future_to_task = {
                executor.submit(self._send_email_task, task): task 
                for task in tasks
            }
            
            completed = 0
            for future in as_completed(future_to_task):
                if self.stop_flag:
                    # 取消剩余任务
                    for f in future_to_task:
                        f.cancel()
                    break
                
                task = future_to_task[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    completed += 1
                    if progress_callback:
                        progress_callback(completed, total_emails, result)
                    
                    # 随机延迟
                    if delay_range[1] > 0:
                        delay = random.uniform(delay_range[0], delay_range[1])
                        time.sleep(delay)
                        
                except Exception as e:
                    logger.error(f"任务执行失败: {e}")
                    results.append({
                        'success': False,
                        'recipient': task['recipient'],
                        'sender': task['sender_email'],
                        'error': str(e),
                        'attempts': 0,
                        'send_time': None
                    })
        
        # 生成统计报告
        self._generate_stats_report()
        
        return results
    
    def _send_email_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """发送邮件任务"""
        return self.send_single_email(
            task['sender_email'],
            task['sender_password'], 
            task['sender_name'],
            task['recipient'],
            task['subject'],
            task['content'],
            task['is_html'],
            task['attachments'],
            task['max_retries']
        )
    
    def stop_sending(self):
        """停止发送"""
        self.stop_flag = True
        logger.info("邮件发送已停止")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        stats['current_sent'] = self.sent_count
        stats['current_failed'] = self.failed_count
        
        if stats['start_time']:
            elapsed = time.time() - stats['start_time']
            stats['elapsed_time'] = elapsed
            stats['emails_per_minute'] = (self.sent_count / elapsed * 60) if elapsed > 0 else 0
        
        return stats
    
    def _generate_stats_report(self):
        """生成统计报告"""
        stats = self.get_stats()
        
        report = f"""
=== 邮件发送统计报告 ===
发送成功: {stats['total_sent']}
发送失败: {stats['total_failed']}
使用账号数: {len(stats['accounts_used'])}
总耗时: {stats.get('elapsed_time', 0):.2f} 秒
发送速度: {stats.get('emails_per_minute', 0):.2f} 封/分钟

错误统计:
"""
        for error_type, count in stats['error_stats'].items():
            report += f"  {error_type}: {count}\n"
        
        logger.info(report)
        
        # 保存报告到文件
        try:
            with open('logs/send_report.txt', 'w', encoding='utf-8') as f:
                f.write(report)
        except Exception as e:
            logger.error(f"保存报告失败: {e}")

class EmailValidator:
    """邮箱验证器"""
    
    @staticmethod
    def is_valid_email(email: str) -> bool:
        """验证邮箱格式"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}
            
        return bool(re.match(pattern, email))
    
    @staticmethod
    def check_mx_record(domain: str) -> bool:
        """检查MX记录"""
        try:
            import dns.resolver
            mx_records = dns.resolver.resolve(domain, 'MX')
            return len(mx_records) > 0
        except Exception:
            return False
    
    @staticmethod
    def validate_email_list(emails: List[str]) -> Tuple[List[str], List[str]]:
        """批量验证邮箱"""
        valid_emails = []
        invalid_emails = []
        
        for email in emails:
            email = email.strip()
            if EmailValidator.is_valid_email(email):
                domain = email.split('@')[1]
                if EmailValidator.check_mx_record(domain):
                    valid_emails.append(email)
                else:
                    invalid_emails.append(email)
            else:
                invalid_emails.append(email)
        
        return valid_emails, invalid_emails

# 使用示例
if __name__ == "__main__":
    # 测试邮件发送
    sender = EmailSender(max_threads=10)
    
    # 账号列表: (邮箱, 密码, 发件人名称)
    accounts = [
        ("<EMAIL>", "password", "测试用户"),
    ]
    
    # 收件人列表
    recipients = ["<EMAIL>"]
    
    # 发送邮件
    results = sender.send_batch_emails(
        accounts=accounts,
        recipients=recipients,
        subject="测试邮件",
        content="这是一封测试邮件",
        is_html=False,
        delay_range=(1, 3),
        max_retries=3
    )
    
    print(f"发送完成，成功: {sender.sent_count}, 失败: {sender.failed_count}")
            