from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QTableWidget, QTableWidgetItem, QGroupBox
import os
import datetime

MON_DIR = "data/smtpmon"
os.makedirs(MON_DIR, exist_ok=True)

class PluginTab(QWidget):
    tab_name = "SMTP监控"

    def __init__(self):
        super().__init__()
        self.queue = []      # 当前待发信队列（示意）
        self.history = []    # 已发记录
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 队列状态
        queue_box = QGroupBox("实时队列")
        v1 = QVBoxLayout()
        self.queue_table = QTableWidget(0, 4)
        self.queue_table.setHorizontalHeaderLabels(["账号", "收件人", "状态", "时间"])
        v1.addWidget(self.queue_table)
        queue_box.setLayout(v1)
        layout.addWidget(queue_box)

        # 历史日志
        history_box = QGroupBox("发信历史")
        v2 = QVBoxLayout()
        self.history_table = QTableWidget(0, 5)
        self.history_table.setHorizontalHeaderLabels(["账号", "收件人", "结果", "响应", "时间"])
        v2.addWidget(self.history_table)
        history_box.setLayout(v2)
        layout.addWidget(history_box)

        # 控制按钮
        ctrl_layout = QHBoxLayout()
        self.clear_btn = QPushButton("清空监控")
        self.clear_btn.clicked.connect(self.clear_all)
        ctrl_layout.addWidget(self.clear_btn)
        self.export_btn = QPushButton("导出日志")
        self.export_btn.clicked.connect(self.export_log)
        ctrl_layout.addWidget(self.export_btn)
        layout.addLayout(ctrl_layout)

        # 统计区
        self.stats_label = QLabel("统计: --")
        layout.addWidget(self.stats_label)

        self.setLayout(layout)
        self.refresh_ui()

    def update_queue(self, tasks):
        # 可被群发插件实时调用
        self.queue = tasks
        self.refresh_ui()

    def update_history(self, record):
        # 可被群发插件实时调用
        self.history.append(record)
        self.refresh_ui()

    def refresh_ui(self):
        # 更新队列
        self.queue_table.setRowCount(0)
        for t in self.queue:
            self.queue_table.insertRow(self.queue_table.rowCount())
            for i, v in enumerate([t.get("account", ""), t.get("to", ""), t.get("status", ""), t.get("time", "")]):
                self.queue_table.setItem(self.queue_table.rowCount()-1, i, QTableWidgetItem(str(v)))
        # 更新历史
        self.history_table.setRowCount(0)
        for r in self.history[-100:]:
            self.history_table.insertRow(self.history_table.rowCount())
            for i, v in enumerate([r.get("account", ""), r.get("to", ""), r.get("result", ""), r.get("resp", ""), r.get("time", "")]):
                self.history_table.setItem(self.history_table.rowCount()-1, i, QTableWidgetItem(str(v)))
        # 统计
        total = len(self.history)
        ok = sum(1 for r in self.history if r.get("result") == "成功")
        fail = total - ok
        self.stats_label.setText(f"统计：总发信 {total}，成功 {ok}，失败 {fail}")

    def clear_all(self):
        self.queue.clear()
        self.history.clear()
        self.refresh_ui()

    def export_log(self):
        fname = os.path.join(MON_DIR, f"smtp_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        with open(fname, "w", encoding="utf-8") as f:
            for r in self.history:
                f.write(f"{r.get('account','')}->{r.get('to','')}: {r.get('result','')}  {r.get('resp','')}  {r.get('time','')}\n")
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "导出成功", f"SMTP日志已导出到：{fname}")
