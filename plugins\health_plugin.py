from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QVBoxLayout, QTextEdit
import os
import sys
import platform
import shutil
import socket

class PluginTab(QWidget):
    tab_name = "自动体检"

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        self.check_btn = QPushButton("一键体检")
        self.check_btn.clicked.connect(self.run_check)
        layout.addWidget(self.check_btn)
        self.result_box = QTextEdit()
        self.result_box.setReadOnly(True)
        layout.addWidget(self.result_box)
        self.setLayout(layout)

    def run_check(self):
        self.result_box.clear()
        # Python环境
        pyver = sys.version.replace('\n', ' ')
        self.result_box.append(f"Python版本: {pyver}")
        # 依赖检查
        try:
            import PyQt5, requests, openpyxl, dns, socks
            self.result_box.append("依赖检测: ✅ (PyQt5/requests/openpyxl/dns/socks)")
        except ImportError as e:
            self.result_box.append(f"依赖检测: ❌ 缺失: {e}")
        # 磁盘空间
        total, used, free = shutil.disk_usage(os.getcwd())
        self.result_box.append(f"磁盘空间: 总计 {total//2**30}G 可用 {free//2**30}G")
        # 网络
        try:
            socket.create_connection(("8.8.8.8", 53), timeout=3)
            self.result_box.append("网络检测: ✅ 正常")
        except Exception:
            self.result_box.append("网络检测: ❌ 失败")
        # 账号池/配置检测
        ac_ok = os.path.exists("data/accounts.txt")
        conf_ok = os.path.exists("config/settings.json")
        self.result_box.append(f"账号池检测: {'✅' if ac_ok else '❌ 不存在 data/accounts.txt'}")
        self.result_box.append(f"配置检测: {'✅' if conf_ok else '❌ 不存在 config/settings.json'}")
        # 操作系统
        sysinfo = platform.platform()
        self.result_box.append(f"操作系统: {sysinfo}")
        # U盘检测（可选）
        if hasattr(sys, "getwindowsversion"):
            drives = [d + ":\\" for d in "ABCDEFGHIJKLMNOPQRSTUVWXYZ" if os.path.exists(d + ":\\")]
            removable = [d for d in drives if os.path.exists(os.path.join(d, "U盘标记.txt"))]
            if removable:
                self.result_box.append(f"U盘检测: 检测到 {len(removable)} 个U盘（带U盘标记.txt）")
            else:
                self.result_box.append("U盘检测: 未检测到U盘或标记文件")
        # 结果
        self.result_box.append("\n体检完成。如有 ❌ ，请检查环境/依赖/U盘/账号池配置。")
