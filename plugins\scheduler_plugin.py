from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QLineEdit, QFileDialog, QMessageBox, QTableWidget, QTableWidgetItem, QComboBox
import threading
import time
import datetime
import json
import os

SCHEDULE_PATH = "data/schedule.json"
os.makedirs("data", exist_ok=True)

def load_schedules():
    if not os.path.exists(SCHEDULE_PATH):
        return []
    with open(SCHEDULE_PATH, "r", encoding="utf-8") as f:
        return json.load(f)

def save_schedules(schedules):
    with open(SCHEDULE_PATH, "w", encoding="utf-8") as f:
        json.dump(schedules, f, ensure_ascii=False, indent=2)

class PluginTab(QWidget):
    tab_name = "任务计划"

    def __init__(self):
        super().__init__()
        self.schedules = load_schedules()
        self.init_ui()
        self.task_thread = None
        self.running = False

    def init_ui(self):
        layout = QVBoxLayout()

        # 任务表
        self.table = QTableWidget(0, 5)
        self.table.setHorizontalHeaderLabels(["ID", "任务类型", "启动时间", "参数", "状态"])
        layout.addWidget(self.table)

        # 添加任务区
        add_layout = QHBoxLayout()
        self.type_box = QComboBox()
        self.type_box.addItems(["注册", "养号", "群发"])
        add_layout.addWidget(QLabel("任务类型:"))
        add_layout.addWidget(self.type_box)
        self.time_input = QLineEdit()
        self.time_input.setPlaceholderText("格式: 03:30 (24小时制)")
        add_layout.addWidget(QLabel("启动时间:"))
        add_layout.addWidget(self.time_input)
        self.param_input = QLineEdit()
        self.param_input.setPlaceholderText("参数（如数量/路径）")
        add_layout.addWidget(QLabel("参数:"))
        add_layout.addWidget(self.param_input)
        self.add_btn = QPushButton("添加任务")
        self.add_btn.clicked.connect(self.add_task)
        add_layout.addWidget(self.add_btn)
        layout.addLayout(add_layout)

        # 控制按钮
        ctrl_layout = QHBoxLayout()
        self.del_btn = QPushButton("删除选中任务")
        self.del_btn.clicked.connect(self.del_task)
        ctrl_layout.addWidget(self.del_btn)
        self.start_btn = QPushButton("启动任务计划")
        self.start_btn.clicked.connect(self.start_scheduler)
        ctrl_layout.addWidget(self.start_btn)
        self.stop_btn = QPushButton("停止计划")
        self.stop_btn.clicked.connect(self.stop_scheduler)
        self.stop_btn.setEnabled(False)
        ctrl_layout.addWidget(self.stop_btn)
        layout.addLayout(ctrl_layout)

        # 日志区
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        layout.addWidget(self.log_box)

        self.setLayout(layout)
        self.refresh_table()

    def refresh_table(self):
        self.table.setRowCount(0)
        for i, t in enumerate(self.schedules):
            self.table.insertRow(self.table.rowCount())
            for j, k in enumerate(["ID", "type", "time", "param", "status"]):
                val = str(t.get(k, "")) if k != "ID" else str(i+1)
                self.table.setItem(self.table.rowCount()-1, j, QTableWidgetItem(val))

    def add_task(self):
        typ = self.type_box.currentText()
        tstr = self.time_input.text().strip()
        param = self.param_input.text().strip()
        if not tstr or not re_time(tstr):
            QMessageBox.warning(self, "时间格式错误", "时间请用 03:30 (24小时制)")
            return
        task = {"type": typ, "time": tstr, "param": param, "status": "等待"}
        self.schedules.append(task)
        save_schedules(self.schedules)
        self.refresh_table()
        self.log_box.append(f"已添加任务: {typ} {tstr} {param}")

    def del_task(self):
        row = self.table.currentRow()
        if row < 0: return
        del self.schedules[row]
        save_schedules(self.schedules)
        self.refresh_table()
        self.log_box.append("已删除选中任务。")

    def start_scheduler(self):
        if self.running:
            QMessageBox.warning(self, "已运行", "任务计划已经启动中")
            return
        self.running = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.log_box.append("任务计划已启动。")
        self.task_thread = threading.Thread(target=self.run_scheduler)
        self.task_thread.daemon = True
        self.task_thread.start()

    def stop_scheduler(self):
        self.running = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.log_box.append("任务计划已停止。")

    def run_scheduler(self):
        while self.running:
            now = datetime.datetime.now().strftime("%H:%M")
            for task in self.schedules:
                if task["status"] == "等待" and now == task["time"]:
                    self.log_box.append(f"【自动任务启动】{task['type']} 参数:{task['param']}")
                    # 占位：这里调用各插件自动任务
                    task["status"] = "已完成"
                    save_schedules(self.schedules)
                    self.refresh_table()
            time.sleep(30)

def re_time(ts):
    try:
        time.strptime(ts, "%H:%M")
        return True
    except Exception:
        return False
