import os
import json
import logging
from typing import Any, Dict, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # 默认配置
        self.default_config = {
            "app": {
                "title": "EM Sent 邮件群发工具",
                "version": "2.0.0",
                "author": "Awin",
                "email": "<EMAIL>",
                "window_width": 1200,
                "window_height": 720,
                "theme": "default",
                "language": "zh_CN"
            },
            "email": {
                "smtp_timeout": 30,
                "max_retry": 3,
                "batch_size": 50,
                "delay_between_batch": 2,
                "connection_pool_size": 20,
                "max_threads": 50
            },
            "proxy": {
                "enabled": False,
                "proxy_file": "",
                "rotate_proxy": True,
                "proxy_timeout": 10
            },
            "security": {
                "activation_required": True,
                "max_daily_sends": 10000,
                "log_level": "INFO"
            },
            "paths": {
                "data_dir": "data",
                "logs_dir": "logs",
                "backup_dir": "backup",
                "templates_dir": "templates"
            }
        }
        
        self.config_file = self.config_dir / "app_config.json"
        self._config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 合并默认配置
                merged_config = self._merge_config(self.default_config, config)
                return merged_config
            else:
                # 创建默认配置文件
                self.save_config(self.default_config)
                return self.default_config.copy()
        
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            return self.default_config.copy()
    
    def save_config(self, config: Optional[Dict[str, Any]] = None):
        """保存配置文件"""
        try:
            config_to_save = config if config is not None else self._config
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
            
            if config is not None:
                self._config = config
            
            logger.info("配置已保存")
        
        except Exception as e:
            logger.error(f"配置保存失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        try:
            keys = key.split('.')
            value = self._config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
        
        except Exception:
            return default
    
    def set(self, key: str, value: Any, save: bool = True):
        """设置配置值"""
        try:
            keys = key.split('.')
            config_ref = self._config
            
            # 导航到目标位置
            for k in keys[:-1]:
                if k not in config_ref:
                    config_ref[k] = {}
                config_ref = config_ref[k]
            
            # 设置值
            config_ref[keys[-1]] = value
            
            if save:
                self.save_config()
            
            logger.debug(f"配置已更新: {key} = {value}")
        
        except Exception as e:
            logger.error(f"配置设置失败 {key}: {e}")
    
    def update(self, updates: Dict[str, Any], save: bool = True):
        """批量更新配置"""
        try:
            for key, value in updates.items():
                self.set(key, value, save=False)
            
            if save:
                self.save_config()
        
        except Exception as e:
            logger.error(f"配置批量更新失败: {e}")
    
    def reset_to_default(self, section: Optional[str] = None):
        """重置为默认配置"""
        try:
            if section:
                if section in self.default_config:
                    self._config[section] = self.default_config[section].copy()
            else:
                self._config = self.default_config.copy()
            
            self.save_config()
            logger.info(f"配置已重置: {section or '全部'}")
        
        except Exception as e:
            logger.error(f"配置重置失败: {e}")
    
    def export_config(self, file_path: str):
        """导出配置"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置已导出到: {file_path}")
        
        except Exception as e:
            logger.error(f"配置导出失败: {e}")
            raise
    
    def import_config(self, file_path: str):
        """导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 合并配置
            self._config = self._merge_config(self.default_config, imported_config)
            self.save_config()
            
            logger.info(f"配置已从 {file_path} 导入")
        
        except Exception as e:
            logger.error(f"配置导入失败: {e}")
            raise
    
    def _merge_config(self, default: Dict, custom: Dict) -> Dict:
        """合并配置字典"""
        result = default.copy()
        
        for key, value in custom.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        try:
            # 验证必需的配置项
            required_keys = [
                "app.title",
                "app.version",
                "email.smtp_timeout",
                "paths.data_dir"
            ]
            
            for key in required_keys:
                if self.get(key) is None:
                    logger.error(f"缺少必需的配置项: {key}")
                    return False
            
            # 验证数值范围
            if not (1 <= self.get("email.max_threads", 50) <= 200):
                logger.error("email.max_threads 必须在 1-200 之间")
                return False
            
            if not (1 <= self.get("email.smtp_timeout", 30) <= 300):
                logger.error("email.smtp_timeout 必须在 1-300 之间")
                return False
            
            return True
        
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
    
    def get_email_config(self) -> Dict[str, Any]:
        """获取邮件相关配置"""
        return {
            'smtp_timeout': self.get('email.smtp_timeout', 30),
            'max_retry': self.get('email.max_retry', 3),
            'batch_size': self.get('email.batch_size', 50),
            'delay_between_batch': self.get('email.delay_between_batch', 2),
            'connection_pool_size': self.get('email.connection_pool_size', 20),
            'max_threads': self.get('email.max_threads', 50)
        }
    
    def get_proxy_config(self) -> Dict[str, Any]:
        """获取代理相关配置"""
        return {
            'enabled': self.get('proxy.enabled', False),
            'proxy_file': self.get('proxy.proxy_file', ''),
            'rotate_proxy': self.get('proxy.rotate_proxy', True),
            'proxy_timeout': self.get('proxy.proxy_timeout', 10)
        }
    
    def get_app_config(self) -> Dict[str, Any]:
        """获取应用程序配置"""
        return {
            'title': self.get('app.title', 'EM Sent 邮件群发工具'),
            'version': self.get('app.version', '2.0.0'),
            'author': self.get('app.author', 'Awin'),
            'email': self.get('app.email', '<EMAIL>'),
            'window_width': self.get('app.window_width', 1200),
            'window_height': self.get('app.window_height', 720),
            'theme': self.get('app.theme', 'default'),
            'language': self.get('app.language', 'zh_CN')
        }
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        try:
            dirs = [
                self.get('paths.data_dir', 'data'),
                self.get('paths.logs_dir', 'logs'),
                self.get('paths.backup_dir', 'backup'),
                self.get('paths.templates_dir', 'templates')
            ]
            
            for dir_path in dirs:
                Path(dir_path).mkdir(exist_ok=True)
            
            logger.info("目录结构检查完成")
        
        except Exception as e:
            logger.error(f"目录创建失败: {e}")

# 全局配置管理器实例
config_manager = ConfigManager()

# 便捷函数
def get_config(key: str, default: Any = None) -> Any:
    """获取配置值"""
    return config_manager.get(key, default)

def set_config(key: str, value: Any, save: bool = True):
    """设置配置值"""
    config_manager.set(key, value, save)

def save_config():
    """保存配置"""
    config_manager.save_config()

def reset_config(section: Optional[str] = None):
    """重置配置"""
    config_manager.reset_to_default(section)

# 初始化
if __name__ == "__main__":
    # 测试配置管理器
    print("配置管理器测试")
    
    # 确保目录存在
    config_manager.ensure_directories()
    
    # 验证配置
    if config_manager.validate_config():
        print("配置验证通过")
    else:
        print("配置验证失败")
    
    # 打印当前配置
    print("\n当前应用配置:")
    app_config = config_manager.get_app_config()
    for key, value in app_config.items():
        print(f"  {key}: {value}")
    
    print("\n当前邮件配置:")
    email_config = config_manager.get_email_config()
    for key, value in email_config.items():
        print(f"  {key}: {value}")