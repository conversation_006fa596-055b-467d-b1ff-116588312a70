# -*- mode: python ; coding: utf-8 -*-
"""
EM Sent 邮件群发工具 - PyInstaller 打包配置
文件位置: 根目录/build.spec

使用方法:
pip install pyinstaller
pyinstaller build.spec
"""

import os

block_cipher = None

# 收集插件文件
plugin_files = []
if os.path.exists('plugins'):
    for file in os.listdir('plugins'):
        if file.endswith('.py'):
            plugin_files.append((os.path.join('plugins', file), 'plugins'))

# 收集数据文件
data_files = [
    # 配置文件
    ('config', 'config'),
    # 模板文件
    ('templates', 'templates'),
    # 资源文件
    ('assets', 'assets'),
]

# 添加插件文件
data_files.extend(plugin_files)

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=data_files,
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'email.mime.text',
        'email.mime.multipart',
        'smtplib',
        'imaplib',
        'dns.resolver',
        'socks',
        'openpyxl',
        'requests',
        'ssl',
        'socket',
        'threading',
        'json',
        'time',
        'datetime',
        'logging',
        'os',
        'sys',
        'pathlib',
        'shutil',
        'zipfile',
        'random',
        'traceback',
        'concurrent.futures',
        'queue',
        're',
        'platform',
        'cryptography',
        'urllib3',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy', 
        'pandas',
        'scipy',
        'tkinter',
        '_tkinter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='EM_Sent_Tool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)