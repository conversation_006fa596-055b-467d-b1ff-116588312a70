from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QListWidget, QListWidgetItem, QLineEdit, QMessageBox, QFileDialog
import json
import os

FLOW_PATH = "data/flows.json"
os.makedirs("data", exist_ok=True)

FLOW_BLOCKS = [
    {"name": "导入账号池", "param": "账号文件路径"},
    {"name": "账号检测", "param": "检测级别"},
    {"name": "AI生成内容", "param": "主题/模板"},
    {"name": "内容扰动", "param": "批量生成数量"},
    {"name": "批量群发", "param": "线程数/延迟"},
    {"name": "导出日志", "param": "导出路径"},
    {"name": "归档快照", "param": "快照名"},
    {"name": "自定义API", "param": "API序号"}
]

def load_flows():
    if not os.path.exists(FLOW_PATH):
        return []
    with open(FLOW_PATH, "r", encoding="utf-8") as f:
        return json.load(f)

def save_flows(flows):
    with open(FLOW_PATH, "w", encoding="utf-8") as f:
        json.dump(flows, f, ensure_ascii=False, indent=2)

class PluginTab(QWidget):
    tab_name = "流程编辑器"

    def __init__(self):
        super().__init__()
        self.flows = load_flows()
        self.current_flow = []
        self.init_ui()

    def init_ui(self):
        layout = QHBoxLayout()
        # 左侧：可选模块
        self.block_list = QListWidget()
        for block in FLOW_BLOCKS:
            item = QListWidgetItem(block["name"])
            self.block_list.addItem(item)
        self.block_list.itemDoubleClicked.connect(self.add_block)
        layout.addWidget(self.block_list)

        # 右侧：已拼流程
        rlayout = QVBoxLayout()
        self.step_box = QTextEdit()
        self.step_box.setReadOnly(True)
        rlayout.addWidget(self.step_box)

        # 参数输入
        playout = QHBoxLayout()
        self.param_input = QLineEdit()
        self.param_input.setPlaceholderText("步骤参数（如线程数/路径/主题等）")
        playout.addWidget(self.param_input)
        self.del_step_btn = QPushButton("撤销一步")
        self.del_step_btn.clicked.connect(self.del_last)
        playout.addWidget(self.del_step_btn)
        rlayout.addLayout(playout)

        # 流程操作
        blayout = QHBoxLayout()
        self.save_btn = QPushButton("保存流程")
        self.save_btn.clicked.connect(self.save_flow)
        blayout.addWidget(self.save_btn)
        self.load_btn = QPushButton("加载流程")
        self.load_btn.clicked.connect(self.load_flow)
        blayout.addWidget(self.load_btn)
        self.run_btn = QPushButton("运行流程")
        self.run_btn.clicked.connect(self.run_flow)
        blayout.addWidget(self.run_btn)
        rlayout.addLayout(blayout)

        layout.addLayout(rlayout)
        self.setLayout(layout)
        self.show_flow()

    def add_block(self, item):
        block = item.text()
        param = self.param_input.text().strip()
        self.current_flow.append({"block": block, "param": param})
        self.show_flow()

    def show_flow(self):
        text = "\n".join([f"{i+1}. {b['block']}  {b['param']}" for i, b in enumerate(self.current_flow)])
        self.step_box.setText(text or "暂无流程")

    def del_last(self):
        if self.current_flow:
            self.current_flow.pop()
        self.show_flow()

    def save_flow(self):
        name, ok = QFileDialog.getSaveFileName(self, "流程命名并保存", "myflow.json", "JSON Files (*.json)")
        if not name:
            return
        flow = {"name": os.path.splitext(os.path.basename(name))[0], "steps": self.current_flow.copy()}
        self.flows.append(flow)
        save_flows(self.flows)
        QMessageBox.information(self, "保存成功", f"流程 {flow['name']} 已保存。")
        self.current_flow = []
        self.show_flow()

    def load_flow(self):
        fname, _ = QFileDialog.getOpenFileName(self, "加载流程", "", "JSON Files (*.json)")
        if not fname:
            return
        with open(fname, "r", encoding="utf-8") as f:
            flow = json.load(f)
        self.current_flow = flow["steps"]
        self.show_flow()

    def run_flow(self):
        # 演示：可后续对接主程序实际批量执行逻辑
        text = "\n".join([f"自动执行: {b['block']}（参数:{b['param']}）" for b in self.current_flow])
        QMessageBox.information(self, "流程运行", text or "暂无可运行流程")
