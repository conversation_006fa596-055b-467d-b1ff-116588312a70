from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QFileDialog, QMessageBox, QComboBox
import os
import json
import datetime

class PluginTab(QWidget):
    tab_name = "统计分析"

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 报表类型
        type_layout = QHBoxLayout()
        self.type_box = QComboBox()
        self.type_box.addItems([
            "账号池", "收件池", "发送结果", "失败原因", "活跃账号", "模板利用率"
        ])
        type_layout.addWidget(QLabel("统计类型:"))
        type_layout.addWidget(self.type_box)
        layout.addLayout(type_layout)

        # 操作按钮
        btn_layout = QHBoxLayout()
        self.analyze_btn = QPushButton("统计分析")
        self.analyze_btn.clicked.connect(self.analyze)
        btn_layout.addWidget(self.analyze_btn)
        self.export_btn = QPushButton("导出报表")
        self.export_btn.clicked.connect(self.export_report)
        btn_layout.addWidget(self.export_btn)
        layout.addLayout(btn_layout)

        # 结果区
        self.result_box = QTextEdit()
        self.result_box.setReadOnly(True)
        layout.addWidget(self.result_box)

        self.setLayout(layout)

    def analyze(self):
        typ = self.type_box.currentText()
        lines = []
        if typ == "账号池":
            path = "data/accounts.txt"
            if os.path.exists(path):
                with open(path, "r", encoding="utf-8") as f:
                    lines = [l.strip() for l in f if l.strip()]
                self.result_box.setText(f"账号总数：{len(lines)}\n示例：\n" + "\n".join(lines[:10]))
            else:
                self.result_box.setText("账号池未找到")
        elif typ == "收件池":
            path = "data/recipients.txt"
            if os.path.exists(path):
                with open(path, "r", encoding="utf-8") as f:
                    lines = [l.strip() for l in f if l.strip()]
                self.result_box.setText(f"收件人总数：{len(lines)}\n示例：\n" + "\n".join(lines[:10]))
            else:
                self.result_box.setText("收件池未找到")
        elif typ == "发送结果":
            path = "data/logs/sender.log"
            ok, fail = 0, 0
            if os.path.exists(path):
                with open(path, "r", encoding="utf-8") as f:
                    for l in f:
                        if "成功" in l:
                            ok += 1
                        elif "失败" in l:
                            fail += 1
                self.result_box.setText(f"成功发送：{ok}，失败：{fail}，总计：{ok+fail}")
            else:
                self.result_box.setText("发送日志未找到")
        elif typ == "失败原因":
            path = "data/logs/sender.log"
            stat = {}
            if os.path.exists(path):
                with open(path, "r", encoding="utf-8") as f:
                    for l in f:
                        if "失败" in l:
                            reason = l.split("失败:")[-1].strip() if "失败:" in l else "未知"
                            stat[reason] = stat.get(reason, 0) + 1
                res = "\n".join([f"{k}: {v}" for k, v in stat.items()])
                self.result_box.setText("失败原因统计：\n" + res if res else "暂无失败数据")
            else:
                self.result_box.setText("发送日志未找到")
        elif typ == "活跃账号":
            path = "data/logs/sender.log"
            stat = {}
            if os.path.exists(path):
                with open(path, "r", encoding="utf-8") as f:
                    for l in f:
                        if "账号:" in l:
                            acc = l.split("账号:")[-1].split()[0]
                            stat[acc] = stat.get(acc, 0) + 1
                res = "\n".join([f"{k}: {v}次" for k, v in sorted(stat.items(), key=lambda x: -x[1])[:10]])
                self.result_box.setText("最活跃账号前10：\n" + res if res else "暂无账号数据")
            else:
                self.result_box.setText("发送日志未找到")
        elif typ == "模板利用率":
            path = "data/logs/sender.log"
            stat = {}
            if os.path.exists(path):
                with open(path, "r", encoding="utf-8") as f:
                    for l in f:
                        if "模板:" in l:
                            tmp = l.split("模板:")[-1].split()[0]
                            stat[tmp] = stat.get(tmp, 0) + 1
                res = "\n".join([f"{k}: {v}次" for k, v in sorted(stat.items(), key=lambda x: -x[1])])
                self.result_box.setText("模板利用率统计：\n" + res if res else "暂无模板数据")
            else:
                self.result_box.setText("发送日志未找到")

    def export_report(self):
        typ = self.type_box.currentText()
        fname, _ = QFileDialog.getSaveFileName(self, "导出报表", f"{typ}_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", "Text Files (*.txt)")
        if not fname:
            return
        try:
            with open(fname, "w", encoding="utf-8") as f:
                f.write(self.result_box.toPlainText())
            QMessageBox.information(self, "导出成功", f"报表已导出到:\n{fname}")
        except Exception as e:
            QMessageBox.warning(self, "导出失败", str(e))
