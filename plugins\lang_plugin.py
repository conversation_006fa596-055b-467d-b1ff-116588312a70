from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QComboBox, QMessageBox
import os
import json

LANG_PATH = "data/langs"
os.makedirs(LANG_PATH, exist_ok=True)

LANGUAGES = {
    "简体中文": "zh_CN.json",
    "English": "en_US.json",
}

DEFAULT_TRANSLATIONS = {
    "zh_CN.json": {
        "欢迎使用": "欢迎使用EM sent批量群发系统！",
        "开始群发": "开始群发",
        "停止": "停止",
        "账号池": "账号池",
        "收件人": "收件人",
        "标题": "标题",
        "正文": "正文",
        "日志": "日志",
        "导入": "导入",
        "导出": "导出",
        "添加模板": "添加模板",
        "批量操作": "批量操作",
        "主题皮肤": "主题皮肤",
        "激活码": "激活码",
        "保存": "保存",
        "AI生成内容": "AI生成内容",
        "黑名单": "黑名单",
        "白名单": "白名单",
        # ...后续可补充所有常用文本
    },
    "en_US.json": {
        "欢迎使用": "Welcome to EM sent Bulk Mailer!",
        "开始群发": "Start Sending",
        "停止": "Stop",
        "账号池": "Accounts",
        "收件人": "Recipients",
        "标题": "Subject",
        "正文": "Content",
        "日志": "Log",
        "导入": "Import",
        "导出": "Export",
        "添加模板": "Add Template",
        "批量操作": "Batch Actions",
        "主题皮肤": "Theme",
        "激活码": "Activation Code",
        "保存": "Save",
        "AI生成内容": "AI Generate",
        "黑名单": "Blacklist",
        "白名单": "Whitelist",
        # ...continue for all UI strings
    }
}

# 初始化语言包文件
for lang, fname in LANGUAGES.items():
    path = os.path.join(LANG_PATH, fname)
    if not os.path.exists(path):
        with open(path, "w", encoding="utf-8") as f:
            json.dump(DEFAULT_TRANSLATIONS[fname], f, ensure_ascii=False, indent=2)

class PluginTab(QWidget):
    tab_name = "多语言"

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        hl = QHBoxLayout()
        self.lang_box = QComboBox()
        self.lang_box.addItems(list(LANGUAGES.keys()))
        hl.addWidget(QLabel("选择语言:"))
        hl.addWidget(self.lang_box)
        self.apply_btn = QPushButton("切换")
        self.apply_btn.clicked.connect(self.apply_lang)
        hl.addWidget(self.apply_btn)
        layout.addLayout(hl)
        self.tips = QLabel("注：所有界面元素会刷新成对应语言。可自定义data/langs下的词典文件。")
        layout.addWidget(self.tips)
        self.setLayout(layout)

    def apply_lang(self):
        lang = self.lang_box.currentText()
        path = os.path.join(LANG_PATH, LANGUAGES[lang])
        if not os.path.exists(path):
            QMessageBox.warning(self, "语言包缺失", f"找不到语言包文件: {path}")
            return
        # 简单模拟全局语言刷新
        QMessageBox.information(self, "已切换", f"语言已切换为: {lang}\n（演示：重启主界面后生效，后期可主程序实时刷新）")
