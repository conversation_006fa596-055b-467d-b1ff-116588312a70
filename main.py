import os
for folder in ["logs", "data", "backup", "templates", "assets"]:
    path = os.path.join(os.path.dirname(__file__), folder)
    if not os.path.exists(path):
        os.makedirs(path)
import os
import sys
import importlib.util
import zipfile
import logging
import traceback
from typing import List, Optional
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTabWidget, QMessageBox, 
    QSplashScreen, QLabel, QVBoxLayout, QWidget
)
from PyQt5.QtGui import QPixmap, QIcon
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 路径配置
if getattr(sys, 'frozen', False):
    BASE_DIR = os.path.dirname(sys.executable)
    RESOURCE_DIR = sys._MEIPASS if hasattr(sys, '_MEIPASS') else BASE_DIR
else:
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    RESOURCE_DIR = BASE_DIR

PLUGINS_DIR = os.path.join(BASE_DIR, "plugins")
CONFIG_DIR = os.path.join(BASE_DIR, "config")
DATA_DIR = os.path.join(BASE_DIR, "data")
LOGS_DIR = os.path.join(BASE_DIR, "logs")

# 确保目录存在
for directory in [PLUGINS_DIR, CONFIG_DIR, DATA_DIR, LOGS_DIR]:
    os.makedirs(directory, exist_ok=True)

class PluginLoader(QThread):
    """异步插件加载器"""
    plugin_loaded = pyqtSignal(object, str)
    loading_finished = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def run(self):
        try:
            plugins = self.load_plugins()
            self.loading_finished.emit(plugins)
        except Exception as e:
            self.error_occurred.emit(str(e))
            logger.error(f"插件加载失败: {e}")
    
    def load_plugins(self) -> List:
        """动态加载插件"""
        plugins = []
        
        if not os.path.exists(PLUGINS_DIR):
            logger.warning(f"插件目录不存在: {PLUGINS_DIR}")
            return plugins
        
        # 加载.py插件文件
        for filename in os.listdir(PLUGINS_DIR):
            try:
                if filename.endswith(".py") and not filename.startswith("__"):
                    self._load_python_plugin(filename, plugins)
                elif filename.endswith(".zip"):
                    self._load_zip_plugin(filename, plugins)
            except Exception as e:
                logger.error(f"加载插件 {filename} 失败: {e}")
                continue
        
        logger.info(f"成功加载 {len(plugins)} 个插件")
        return plugins
    
    def _load_python_plugin(self, filename: str, plugins: List):
        """加载Python插件"""
        module_name = filename[:-3]
        file_path = os.path.join(PLUGINS_DIR, filename)
        
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        if spec is None:
            raise ImportError(f"无法创建模块规范: {filename}")
        
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        if hasattr(module, "PluginTab"):
            plugins.append(module)
            self.plugin_loaded.emit(module, filename)
            logger.info(f"已加载插件: {filename}")
    
    def _load_zip_plugin(self, filename: str, plugins: List):
        """加载ZIP压缩插件"""
        zip_path = os.path.join(PLUGINS_DIR, filename)
        extract_dir = os.path.join(PLUGINS_DIR, f"zip_{filename[:-4]}")
        
        # 解压ZIP文件
        if not os.path.exists(extract_dir):
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
        
        # 加载解压后的Python文件
        for extracted_file in os.listdir(extract_dir):
            if extracted_file.endswith(".py"):
                module_name = extracted_file[:-3]
                file_path = os.path.join(extract_dir, extracted_file)
                
                spec = importlib.util.spec_from_file_location(module_name, file_path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                if hasattr(module, "PluginTab"):
                    plugins.append(module)
                    logger.info(f"已从ZIP加载插件: {extracted_file}")

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.plugins = []
        self.plugin_tabs = {}
        self.init_ui()
        self.load_plugins_async()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("EM Sent 邮件群发工具 v2.0 - 作者: Awin - <EMAIL>")
        self.setMinimumSize(1000, 600)
        self.resize(1200, 720)
        
        # 设置图标
        icon_path = os.path.join(RESOURCE_DIR, "assets", "icon.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # 创建中央标签页窗口
        self.tabs = QTabWidget()
        self.tabs.setTabsClosable(False)
        self.tabs.setMovable(True)
        self.setCentralWidget(self.tabs)
        
        # 添加欢迎页面
        self.add_welcome_tab()
        
        # 设置状态栏
        self.statusBar().showMessage("正在加载插件...")
    
    def add_welcome_tab(self):
        """添加欢迎页面"""
        welcome_widget = QWidget()
        layout = QVBoxLayout()
        
        welcome_label = QLabel("""
        <h1>欢迎使用 EM Sent 邮件群发工具</h1>
        <p><b>版本:</b> 2.0</p>
        <p><b>作者:</b> Awin</p>
        <p><b>邮箱:</b> <EMAIL></p>
        <hr>
        <p>正在加载插件系统，请稍候...</p>
        <p>本工具包含25个功能插件，支持邮件群发、账号管理、AI内容生成等功能。</p>
        """)
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setWordWrap(True)
        
        layout.addWidget(welcome_label)
        welcome_widget.setLayout(layout)
        
        self.tabs.addTab(welcome_widget, "欢迎")
    
    def load_plugins_async(self):
        """异步加载插件"""
        self.plugin_loader = PluginLoader()
        self.plugin_loader.plugin_loaded.connect(self.on_plugin_loaded)
        self.plugin_loader.loading_finished.connect(self.on_loading_finished)
        self.plugin_loader.error_occurred.connect(self.on_loading_error)
        self.plugin_loader.start()
    
    def on_plugin_loaded(self, plugin_module, filename):
        """单个插件加载完成"""
        pass  # 可以在这里显示加载进度
    
    def on_loading_finished(self, plugins):
        """所有插件加载完成"""
        self.plugins = plugins
        self.create_plugin_tabs()
        self.statusBar().showMessage(f"已加载 {len(plugins)} 个插件")
        
        # 移除欢迎页面
        if self.tabs.count() > 0:
            self.tabs.removeTab(0)
    
    def on_loading_error(self, error_msg):
        """插件加载错误"""
        QMessageBox.critical(self, "插件加载错误", f"插件加载失败:\n{error_msg}")
        self.statusBar().showMessage("插件加载失败")
    
    def create_plugin_tabs(self):
        """创建插件标签页"""
        # 按优先级排序插件
        priority_order = [
            "群发邮件", "批量群发", "账号管理", "收件人检测", 
            "内容模板库", "AI内容生成", "注册机", "养号器",
            "日志与报表", "统计分析", "SMTP监控", "任务计划"
        ]
        
        # 先添加优先级插件
        added_plugins = set()
        for priority_name in priority_order:
            for plugin in self.plugins:
                if hasattr(plugin, "PluginTab"):
                    try:
                        tab_instance = plugin.PluginTab()
                        tab_name = getattr(tab_instance, "tab_name", plugin.__name__)
                        
                        if tab_name == priority_name:
                            self.tabs.addTab(tab_instance, tab_name)
                            self.plugin_tabs[tab_name] = tab_instance
                            added_plugins.add(plugin)
                            break
                    except Exception as e:
                        logger.error(f"创建插件标签页失败 {plugin.__name__}: {e}")
        
        # 添加其余插件
        for plugin in self.plugins:
            if plugin not in added_plugins and hasattr(plugin, "PluginTab"):
                try:
                    tab_instance = plugin.PluginTab()
                    tab_name = getattr(tab_instance, "tab_name", plugin.__name__)
                    self.tabs.addTab(tab_instance, tab_name)
                    self.plugin_tabs[tab_name] = tab_instance
                except Exception as e:
                    logger.error(f"创建插件标签页失败 {plugin.__name__}: {e}")
    
    def closeEvent(self, event):
        """关闭事件处理"""
        reply = QMessageBox.question(
            self, '确认退出', 
            '确定要退出EM Sent邮件群发工具吗？',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 清理资源
            logger.info("应用程序正常退出")
            event.accept()
        else:
            event.ignore()

def create_splash_screen():
    """创建启动画面"""
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(Qt.white)
    
    splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
    splash.setMask(splash_pix.mask())
    splash.show()
    
    splash.showMessage(
        "EM Sent 邮件群发工具 v2.0\n正在启动...",
        Qt.AlignHCenter | Qt.AlignBottom,
        Qt.black
    )
    
    return splash

def main():
    """主函数"""
    try:
        # 创建应用程序实例
        app = QApplication(sys.argv)
        app.setApplicationName("EM Sent 邮件群发工具")
        app.setApplicationVersion("2.0")
        app.setOrganizationName("Awin Studio")
        
        # 设置应用样式
        app.setStyle('Fusion')
        
        # 创建启动画面
        splash = create_splash_screen()
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 延迟显示主窗口
        QTimer.singleShot(2000, lambda: [splash.close(), main_window.show()])
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.critical(f"应用程序启动失败: {e}")
        traceback.print_exc()
        if 'app' in locals():
            QMessageBox.critical(None, "启动错误", f"应用程序启动失败:\n{str(e)}")

if __name__ == "__main__":
    main()