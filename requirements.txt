# EM Sent 邮件群发工具依赖包
# 文件位置: 根目录/requirements.txt

# GUI框架
PyQt5>=5.15.7

# 网络请求
requests>=2.28.0

# Excel文件处理
openpyxl>=3.0.10
xlrd>=2.0.1

# DNS解析
dnspython>=2.2.1

# 代理支持
PySocks>=1.7.1

# 邮件处理
email-validator>=1.3.0

# 图像处理
Pillow>=9.0.0

# 加密支持
cryptography>=3.4.8

# JSON配置
jsonschema>=4.6.0

# 日志增强
colorlog>=6.6.0

# 进度条
tqdm>=4.64.0

# 时间处理
python-dateutil>=2.8.2

# 文件监控
watchdog>=2.1.9

# HTTP服务器(用于状态监控)
flask>=2.1.0

# 数据库支持(可选)
sqlite3

# 系统信息
psutil>=5.9.0

# 二维码生成(用于激活)
qrcode>=7.3.1

# 机器学习库(用于AI功能,可选)
# scikit-learn>=1.1.0
# numpy>=1.21.0

# 打包工具
pyinstaller>=5.0

# 测试工具
pytest>=7.0.0
pytest-qt>=4.0.0

# 代码质量
flake8>=4.0.0
black>=22.0.0

# 类型检查
mypy>=0.950