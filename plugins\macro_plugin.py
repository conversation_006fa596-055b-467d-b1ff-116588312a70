from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QLineEdit, QComboBox, QMessageBox, QFileDialog
import json
import os

MACRO_PATH = "data/macros.json"
os.makedirs("data", exist_ok=True)

def load_macros():
    if not os.path.exists(MACRO_PATH):
        return []
    with open(MACRO_PATH, "r", encoding="utf-8") as f:
        return json.load(f)

def save_macros(macros):
    with open(MACRO_PATH, "w", encoding="utf-8") as f:
        json.dump(macros, f, ensure_ascii=False, indent=2)

AVAILABLE_ACTIONS = [
    "导入账号池", "导入收件池", "账号检测", "AI生成内容", "批量群发", "导出日志", "批量归档", "环境体检", "自定义API"
]

class PluginTab(QWidget):
    tab_name = "批量宏录制"

    def __init__(self):
        super().__init__()
        self.macros = load_macros()
        self.current_macro = []
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        # 宏列表
        hl1 = QHBoxLayout()
        self.macro_box = QComboBox()
        self.macro_box.addItems([m["name"] for m in self.macros] or ["无宏"])
        hl1.addWidget(QLabel("已保存宏:"))
        hl1.addWidget(self.macro_box)
        self.run_btn = QPushButton("运行宏")
        self.run_btn.clicked.connect(self.run_macro)
        hl1.addWidget(self.run_btn)
        self.del_btn = QPushButton("删除宏")
        self.del_btn.clicked.connect(self.del_macro)
        hl1.addWidget(self.del_btn)
        layout.addLayout(hl1)

        # 编辑区
        hl2 = QHBoxLayout()
        self.action_box = QComboBox()
        self.action_box.addItems(AVAILABLE_ACTIONS)
        hl2.addWidget(QLabel("操作动作:"))
        hl2.addWidget(self.action_box)
        self.param_input = QLineEdit()
        self.param_input.setPlaceholderText("参数（如路径/数量）")
        hl2.addWidget(self.param_input)
        self.add_act_btn = QPushButton("添加步骤")
        self.add_act_btn.clicked.connect(self.add_step)
        hl2.addWidget(self.add_act_btn)
        layout.addLayout(hl2)

        self.step_box = QTextEdit()
        self.step_box.setReadOnly(True)
        layout.addWidget(self.step_box)

        # 宏保存/导入导出
        btn_layout = QHBoxLayout()
        self.save_macro_btn = QPushButton("保存为宏")
        self.save_macro_btn.clicked.connect(self.save_macro)
        btn_layout.addWidget(self.save_macro_btn)
        self.export_btn = QPushButton("导出宏")
        self.export_btn.clicked.connect(self.export_macro)
        btn_layout.addWidget(self.export_btn)
        self.import_btn = QPushButton("导入宏")
        self.import_btn.clicked.connect(self.import_macro)
        btn_layout.addWidget(self.import_btn)
        layout.addLayout(btn_layout)

        self.setLayout(layout)
        self.show_steps()

    def add_step(self):
        action = self.action_box.currentText()
        param = self.param_input.text().strip()
        self.current_macro.append({"action": action, "param": param})
        self.show_steps()

    def show_steps(self):
        text = "\n".join([f"{i+1}. {s['action']}  {s['param']}" for i, s in enumerate(self.current_macro)])
        self.step_box.setText(text or "暂无步骤")

    def save_macro(self):
        name, ok = QFileDialog.getSaveFileName(self, "宏命名并保存", "macro.json", "JSON Files (*.json)")
        if not name:
            return
        macro = {"name": os.path.splitext(os.path.basename(name))[0], "steps": self.current_macro.copy()}
        self.macros.append(macro)
        save_macros(self.macros)
        self.macro_box.addItem(macro["name"])
        QMessageBox.information(self, "保存成功", f"宏 {macro['name']} 已保存。")
        self.current_macro = []
        self.show_steps()

    def run_macro(self):
        idx = self.macro_box.currentIndex()
        if idx < 0 or not self.macros:
            QMessageBox.warning(self, "无宏", "未选择有效宏。")
            return
        steps = self.macros[idx]["steps"]
        for i, step in enumerate(steps):
            # 这里只做演示，真实集成需和主流程联动
            print(f"【自动操作】{i+1}: {step['action']} 参数:{step['param']}")
        QMessageBox.information(self, "自动执行", "所有宏步骤已模拟完成（主流程对接后自动批量操作）")

    def del_macro(self):
        idx = self.macro_box.currentIndex()
        if idx < 0 or not self.macros:
            return
        name = self.macros[idx]["name"]
        del self.macros[idx]
        save_macros(self.macros)
        self.macro_box.removeItem(idx)
        QMessageBox.information(self, "删除成功", f"宏 {name} 已删除。")

    def export_macro(self):
        idx = self.macro_box.currentIndex()
        if idx < 0 or not self.macros:
            return
        macro = self.macros[idx]
        fname, _ = QFileDialog.getSaveFileName(self, "导出宏", f"{macro['name']}.json", "JSON Files (*.json)")
        if not fname:
            return
        with open(fname, "w", encoding="utf-8") as f:
            json.dump(macro, f, ensure_ascii=False, indent=2)
        QMessageBox.information(self, "导出成功", f"宏已导出到:\n{fname}")

    def import_macro(self):
        fname, _ = QFileDialog.getOpenFileName(self, "导入宏", "", "JSON Files (*.json)")
        if not fname:
            return
        with open(fname, "r", encoding="utf-8") as f:
            macro = json.load(f)
        self.macros.append(macro)
        save_macros(self.macros)
        self.macro_box.addItem(macro["name"])
        QMessageBox.information(self, "导入成功", f"宏 {macro['name']} 已导入。")
