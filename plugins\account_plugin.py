from PyQt5.QtWidgets import QWidget, QVBoxLayout, QPushButton, QTextEdit, QFileDialog, QLabel, QHBoxLayout

class PluginTab(QWidget):
    tab_name = "账号管理"
    def __init__(self):
        super().__init__()
        self.init_ui()
    def init_ui(self):
        layout = QVBoxLayout()
        box = QHBoxLayout()
        self.import_btn = QPushButton("导入账号TXT")
        self.import_btn.clicked.connect(self.import_accounts)
        box.addWidget(self.import_btn)
        self.accounts_view = QTextEdit()
        layout.addLayout(box)
        layout.addWidget(QLabel("账号列表："))
        layout.addWidget(self.accounts_view)
        self.setLayout(layout)
    def import_accounts(self):
        fname, _ = QFileDialog.getOpenFileName(self, "选择TXT", "", "TXT Files (*.txt)")
        if fname:
            with open(fname, encoding="utf-8") as f:
                content = f.read()
            self.accounts_view.setText(content)
