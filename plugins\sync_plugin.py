from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLineEdit, QPushButton, QTextEdit, QLabel, QFileDialog
import os, shutil

class PluginTab(QWidget):
    tab_name = "云/U盘同步"
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        layout.addWidget(QLabel("目标同步路径（可填U盘/网盘本地文件夹）："))
        self.path_edit = QLineEdit()
        layout.addWidget(self.path_edit)
        self.btn = QPushButton("一键同步")
        self.btn.clicked.connect(self.sync)
        layout.addWidget(self.btn)
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        layout.addWidget(self.log_box)
        self.setLayout(layout)

    def sync(self):
        path = self.path_edit.text().strip()
        if not path or not os.path.exists(path):
            self.log_box.append("目标路径不存在，请检查。")
            return
        base = os.getcwd()
        for d in ["plugins", "data", "config", "backup"]:
            src = os.path.join(base, d)
            dst = os.path.join(path, d)
            if os.path.exists(dst):
                shutil.rmtree(dst)
            shutil.copytree(src, dst)
        self.log_box.append(f"全部插件/数据已同步到 {path}")
