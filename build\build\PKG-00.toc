('C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\build\\build\\EM_Sent_Tool.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\build\\build\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - '
   '副本\\build\\build\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - '
   '副本\\build\\build\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - '
   '副本\\build\\build\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - '
   '副本\\build\\build\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - '
   '副本\\build\\build\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main', 'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\main.py', 'PYSOURCE'),
  ('python313.dll', 'C:\\Program Files\\Python313\\python313.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Program Files\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program Files\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'C:\\Program Files\\Python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Program Files\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Program Files\\Python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Program Files\\Python313\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\sip.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program Files\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program Files\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program Files\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll', 'C:\\Program Files\\Python313\\python3.dll', 'BINARY'),
  ('libssl-3.dll',
   'C:\\Program Files\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('config\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\config\\__init__.py',
   'DATA'),
  ('config\\config_manager.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\config\\config_manager.py',
   'DATA'),
  ('plugins\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\__init__.py',
   'DATA'),
  ('plugins\\account_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\account_plugin.py',
   'DATA'),
  ('plugins\\ai_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\ai_plugin.py',
   'DATA'),
  ('plugins\\archive_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\archive_plugin.py',
   'DATA'),
  ('plugins\\backup_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\backup_plugin.py',
   'DATA'),
  ('plugins\\checker_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\checker_plugin.py',
   'DATA'),
  ('plugins\\config_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\config_plugin.py',
   'DATA'),
  ('plugins\\excel_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\excel_plugin.py',
   'DATA'),
  ('plugins\\extendapi_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\extendapi_plugin.py',
   'DATA'),
  ('plugins\\flow_editor_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - '
   '副本\\plugins\\flow_editor_plugin.py',
   'DATA'),
  ('plugins\\health_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\health_plugin.py',
   'DATA'),
  ('plugins\\lang_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\lang_plugin.py',
   'DATA'),
  ('plugins\\log_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\log_plugin.py',
   'DATA'),
  ('plugins\\macro_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\macro_plugin.py',
   'DATA'),
  ('plugins\\mailer_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\mailer_plugin.py',
   'DATA'),
  ('plugins\\notify_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\notify_plugin.py',
   'DATA'),
  ('plugins\\proxy_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\proxy_plugin.py',
   'DATA'),
  ('plugins\\register_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\register_plugin.py',
   'DATA'),
  ('plugins\\report_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\report_plugin.py',
   'DATA'),
  ('plugins\\scheduler_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\scheduler_plugin.py',
   'DATA'),
  ('plugins\\smtpmon_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\smtpmon_plugin.py',
   'DATA'),
  ('plugins\\sync_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\sync_plugin.py',
   'DATA'),
  ('plugins\\template_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\template_plugin.py',
   'DATA'),
  ('plugins\\theme_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\theme_plugin.py',
   'DATA'),
  ('plugins\\ui_custom_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\ui_custom_plugin.py',
   'DATA'),
  ('plugins\\update_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\update_plugin.py',
   'DATA'),
  ('plugins\\养号_plugin.py',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - 副本\\plugins\\养号_plugin.py',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography-45.0.5.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography-45.0.5.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography-45.0.5.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography-45.0.5.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography-45.0.5.dist-info\\INSTALLER',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\EM_Sent_Tool - '
   '副本\\build\\build\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
