import os
import shutil

def sync_to_usb(usb_path):
    """同步整个项目到U盘usb_path下的EM sent/"""
    base = os.getcwd()
    target = os.path.join(usb_path, "EM sent")
    if not os.path.exists(target):
        os.makedirs(target)
    for d in ["plugins", "data", "config", "backup"]:
        src = os.path.join(base, d)
        dst = os.path.join(target, d)
        if os.path.exists(dst):
            shutil.rmtree(dst)
        shutil.copytree(src, dst)
    print("已同步全部主数据/插件到U盘:", target)

def sync_to_cloud(cloud_path):
    # cloud_path支持网盘本地同步文件夹，比如 OneDrive/EM sent/、坚果云同步目录等
    sync_to_usb(cloud_path)

if __name__ == "__main__":
    usb = input("输入U盘盘符（如E:）或云盘本地同步目录路径：").strip()
    if not usb:
        print("未输入目标路径！")
    else:
        sync_to_usb(usb)
