from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QLineEdit, QFileDialog, QMessageBox, QComboBox
import json
import requests
import os

EXTAPI_PATH = "config/extend_apis.json"
os.makedirs("config", exist_ok=True)

def load_apis():
    if not os.path.exists(EXTAPI_PATH):
        return []
    with open(EXTAPI_PATH, "r", encoding="utf-8") as f:
        return json.load(f)

def save_apis(apis):
    with open(EXTAPI_PATH, "w", encoding="utf-8") as f:
        json.dump(apis, f, ensure_ascii=False, indent=2)

class PluginTab(QWidget):
    tab_name = "API扩展"

    def __init__(self):
        super().__init__()
        self.apis = load_apis()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 列表
        self.api_table = QTextEdit()
        self.api_table.setReadOnly(True)
        layout.addWidget(QLabel("已集成API（批量可扩展）："))
        layout.addWidget(self.api_table)

        # 添加区
        add_layout = QHBoxLayout()
        self.api_name_input = QLineEdit()
        self.api_name_input.setPlaceholderText("API名称")
        add_layout.addWidget(self.api_name_input)
        self.api_url_input = QLineEdit()
        self.api_url_input.setPlaceholderText("接口URL")
        add_layout.addWidget(self.api_url_input)
        self.api_type_box = QComboBox()
        self.api_type_box.addItems(["GET", "POST"])
        add_layout.addWidget(self.api_type_box)
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("API Key(选填)")
        add_layout.addWidget(self.api_key_input)
        self.add_btn = QPushButton("添加API")
        self.add_btn.clicked.connect(self.add_api)
        add_layout.addWidget(self.add_btn)
        layout.addLayout(add_layout)

        # 调用测试区
        call_layout = QHBoxLayout()
        self.api_idx_input = QLineEdit()
        self.api_idx_input.setPlaceholderText("API序号")
        call_layout.addWidget(QLabel("测试API序号:"))
        call_layout.addWidget(self.api_idx_input)
        self.call_btn = QPushButton("测试调用")
        self.call_btn.clicked.connect(self.call_api)
        call_layout.addWidget(self.call_btn)
        layout.addLayout(call_layout)

        # 结果
        self.result_box = QTextEdit()
        self.result_box.setReadOnly(True)
        layout.addWidget(self.result_box)

        self.setLayout(layout)
        self.refresh_table()

    def refresh_table(self):
        info = []
        for i, api in enumerate(self.apis):
            info.append(f"{i+1}. [{api['type']}] {api['name']} - {api['url']}")
        self.api_table.setText("\n".join(info) if info else "暂无API")

    def add_api(self):
        name = self.api_name_input.text().strip()
        url = self.api_url_input.text().strip()
        typ = self.api_type_box.currentText()
        key = self.api_key_input.text().strip()
        if not name or not url:
            QMessageBox.warning(self, "缺少参数", "API名称和URL不能为空")
            return
        self.apis.append({"name": name, "url": url, "type": typ, "key": key})
        save_apis(self.apis)
        self.refresh_table()

    def call_api(self):
        idx = self.api_idx_input.text().strip()
        if not idx.isdigit() or int(idx) < 1 or int(idx) > len(self.apis):
            QMessageBox.warning(self, "序号错误", "请输入正确API序号")
            return
        api = self.apis[int(idx)-1]
        try:
            if api["type"] == "GET":
                headers = {"Authorization": api["key"]} if api["key"] else {}
                resp = requests.get(api["url"], headers=headers, timeout=8)
            else:
                headers = {"Authorization": api["key"]} if api["key"] else {}
                resp = requests.post(api["url"], headers=headers, timeout=8)
            self.result_box.setText(f"响应码: {resp.status_code}\n返回内容:\n{resp.text[:1000]}")
        except Exception as e:
            self.result_box.setText(f"调用失败: {e}")
